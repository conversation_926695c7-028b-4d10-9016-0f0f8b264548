# Bordio - Work Management Platform

A comprehensive work management platform built with Next.js 15, TypeScript, and Tailwind CSS. Bordio provides superior team collaboration, optimized project execution workflows, and enhanced productivity tools.

## 🚀 Features

### 🗂️ Project & Task Management
- **Unlimited Projects & Folders** - Hierarchical organization with granular collaboration controls
- **Custom Task Statuses** - Workflow personalization for different team processes
- **Subtasks & Time Estimates** - Task decomposition and effort forecasting
- **Drag-and-Drop Interface** - Intuitive visual manipulation of tasks and schedules

### 📅 Calendar & Scheduling
- **Integrated Calendar View** - Multiple time perspectives (daily, weekly, monthly)
- **Time Blocking** - Proactive time management methodology support
- **Recurring Tasks & Events** - Automated scheduling for repetitive work
- **Google Calendar Sync** - (Coming soon) Bi-directional synchronization capabilities

### 💬 Collaboration Tools
- **In-Task Comments** - Contextual communication with rich features
- **Rich Text Notes** - Documentation and knowledge management
- **File Attachments** - Comprehensive file management with versioning
- **Tagging System** - Flexible metadata and classification system

### ⏱️ Time Tracking & Analytics
- **Built-in Time Tracker** - Timer-based and manual time entry
- **Workload Dashboard** - Resource management and capacity visualization
- **Reports & Analytics** - Comprehensive insights and team productivity metrics

## 🛠️ Technology Stack

- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **UI Components**: Custom component library with Headless UI
- **Icons**: Heroicons
- **Date Handling**: date-fns
- **Drag & Drop**: @dnd-kit (ready for implementation)

## 📁 Project Structure

```
src/
├── app/                    # Next.js App Router pages
│   ├── calendar/          # Calendar view and scheduling
│   ├── projects/          # Project management and Kanban boards
│   ├── reports/           # Analytics and reporting
│   ├── settings/          # User and application settings
│   ├── time-tracking/     # Time tracking interface
│   └── page.tsx           # Dashboard homepage
├── components/            # Reusable UI components
│   ├── layout/           # Layout components (sidebar, navigation)
│   ├── task/             # Task-related components
│   └── ui/               # Base UI components (button, card, etc.)
├── lib/                  # Utility functions and helpers
│   ├── mock-data.ts      # Sample data for development
│   └── utils.ts          # Common utility functions
└── types/                # TypeScript type definitions
    └── index.ts          # Core data types and interfaces
```

## 🚀 Getting Started

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd projectmanagement
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Run the development server**
   ```bash
   npm run dev
   ```

4. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000) to see the application.

## 📊 Current Implementation Status

### ✅ Completed Features
- [x] Dashboard with project overview and task statistics
- [x] Project management with Kanban board and list views
- [x] Calendar interface with monthly view
- [x] Time tracking with timer functionality
- [x] Reports and analytics dashboard
- [x] Settings page with customizable workflows
- [x] Responsive design and dark mode support
- [x] Task detail modal with comments
- [x] User management and team collaboration features

### 🚧 In Progress / Coming Soon
- [ ] Drag-and-drop task management
- [ ] Real-time collaboration features
- [ ] Google Calendar integration
- [ ] File upload and attachment system
- [ ] Advanced filtering and search
- [ ] Mobile app development
- [ ] API development and database integration

## 🎨 Design System

The application uses a consistent design system with:
- **Color Palette**: Customizable themes with semantic color tokens
- **Typography**: Geist Sans and Geist Mono fonts
- **Spacing**: Consistent spacing scale using Tailwind CSS
- **Components**: Reusable UI components with proper accessibility

## 📱 Responsive Design

Bordio is fully responsive and works seamlessly across:
- Desktop computers (1024px+)
- Tablets (768px - 1023px)
- Mobile devices (320px - 767px)

## 🔧 Development

### Available Scripts

- `npm run dev` - Start development server with Turbopack
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint

### Code Quality

- **TypeScript** for type safety
- **ESLint** for code linting
- **Prettier** for code formatting (recommended)
- **Tailwind CSS** for consistent styling

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- Built with [Next.js](https://nextjs.org/)
- UI components inspired by [shadcn/ui](https://ui.shadcn.com/)
- Icons by [Heroicons](https://heroicons.com/)
- Design system based on Tailwind CSS

---

**Note**: This is a demonstration implementation based on the comprehensive PRD. For production use, additional features like authentication, database integration, real-time updates, and API development would be required.
