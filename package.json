{"name": "projectmanagement", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:push": "drizzle-kit push", "db:studio": "drizzle-kit studio"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@google/generative-ai": "^0.24.1", "@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "@neondatabase/serverless": "^1.0.0", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-tabs": "^1.1.12", "@types/bcryptjs": "^2.4.6", "bcryptjs": "^3.0.2", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "drizzle-kit": "^0.31.1", "drizzle-orm": "^0.44.1", "groq-sdk": "^0.23.0", "lucide-react": "^0.511.0", "moment": "^2.30.1", "next": "15.3.3", "next-pwa": "^5.6.0", "pusher": "^5.2.0", "pusher-js": "^8.4.0", "react": "^19.0.0", "react-big-calendar": "^1.19.2", "react-dom": "^19.0.0", "react-dropzone": "^14.3.8", "recharts": "^2.15.3", "socket.io-client": "^4.8.1", "tailwind-merge": "^3.3.0", "workbox-webpack-plugin": "^7.3.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.3", "tailwindcss": "^4", "typescript": "^5"}}