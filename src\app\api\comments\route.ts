import { NextRequest } from 'next/server';
import { db, comments, users } from '@/lib/db';
import { successResponse, errorResponse, validationErrorResponse } from '@/lib/api/response';
import { eq } from 'drizzle-orm';

// GET /api/comments - Get comments for a task
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const taskId = searchParams.get('taskId');

    if (!taskId) {
      return validationErrorResponse('Task ID is required');
    }

    const taskComments = await db
      .select({
        id: comments.id,
        content: comments.content,
        taskId: comments.taskId,
        userId: comments.userId,
        parentCommentId: comments.parentCommentId,
        mentions: comments.mentions,
        createdAt: comments.createdAt,
        updatedAt: comments.updatedAt,
        user: {
          id: users.id,
          name: users.name,
          email: users.email,
          avatar: users.avatar,
        },
      })
      .from(comments)
      .leftJoin(users, eq(comments.userId, users.id))
      .where(eq(comments.taskId, taskId))
      .orderBy(comments.createdAt);

    return successResponse(taskComments);
  } catch (error) {
    console.error('Error fetching comments:', error);
    return errorResponse('Failed to fetch comments', 500);
  }
}

// POST /api/comments - Create a new comment
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { content, taskId, userId, parentCommentId, mentions } = body;

    // Validation
    if (!content || !taskId || !userId) {
      return validationErrorResponse('Content, task ID, and user ID are required');
    }

    // Create comment
    const newComment = await db.insert(comments).values({
      content,
      taskId,
      userId,
      parentCommentId,
      mentions: mentions || [],
    }).returning();

    // Get the comment with user data
    const commentWithUser = await db
      .select({
        id: comments.id,
        content: comments.content,
        taskId: comments.taskId,
        userId: comments.userId,
        parentCommentId: comments.parentCommentId,
        mentions: comments.mentions,
        createdAt: comments.createdAt,
        updatedAt: comments.updatedAt,
        user: {
          id: users.id,
          name: users.name,
          email: users.email,
          avatar: users.avatar,
        },
      })
      .from(comments)
      .leftJoin(users, eq(comments.userId, users.id))
      .where(eq(comments.id, newComment[0].id));

    return successResponse(commentWithUser[0], 'Comment created successfully');
  } catch (error) {
    console.error('Error creating comment:', error);
    return errorResponse('Failed to create comment', 500);
  }
}
