import { NextRequest } from 'next/server';
import { db, projects, projectMembers, users, folders } from '@/lib/db';
import { successResponse, errorResponse, notFoundResponse } from '@/lib/api/response';
import { eq } from 'drizzle-orm';

// GET /api/projects/[id] - Get project by ID with members
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const project = await db
      .select({
        id: projects.id,
        name: projects.name,
        description: projects.description,
        color: projects.color,
        folderId: projects.folderId,
        ownerId: projects.ownerId,
        settings: projects.settings,
        createdAt: projects.createdAt,
        updatedAt: projects.updatedAt,
        owner: {
          id: users.id,
          name: users.name,
          email: users.email,
          avatar: users.avatar,
        },
        folder: {
          id: folders.id,
          name: folders.name,
          color: folders.color,
        },
      })
      .from(projects)
      .leftJoin(users, eq(projects.ownerId, users.id))
      .leftJoin(folders, eq(projects.folderId, folders.id))
      .where(eq(projects.id, params.id));

    if (project.length === 0) {
      return notFoundResponse('Project not found');
    }

    // Get project members
    const members = await db
      .select({
        userId: projectMembers.userId,
        role: projectMembers.role,
        joinedAt: projectMembers.joinedAt,
        user: {
          id: users.id,
          name: users.name,
          email: users.email,
          avatar: users.avatar,
        },
      })
      .from(projectMembers)
      .leftJoin(users, eq(projectMembers.userId, users.id))
      .where(eq(projectMembers.projectId, params.id));

    const projectWithMembers = {
      ...project[0],
      members,
    };

    return successResponse(projectWithMembers);
  } catch (error) {
    console.error('Error fetching project:', error);
    return errorResponse('Failed to fetch project', 500);
  }
}

// PUT /api/projects/[id] - Update project
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json();
    const { name, description, color, folderId, settings } = body;

    // Check if project exists
    const existingProject = await db.select().from(projects).where(eq(projects.id, params.id));
    if (existingProject.length === 0) {
      return notFoundResponse('Project not found');
    }

    // Update project
    const updatedProject = await db
      .update(projects)
      .set({
        name,
        description,
        color,
        folderId,
        settings,
        updatedAt: new Date(),
      })
      .where(eq(projects.id, params.id))
      .returning();

    return successResponse(updatedProject[0], 'Project updated successfully');
  } catch (error) {
    console.error('Error updating project:', error);
    return errorResponse('Failed to update project', 500);
  }
}

// DELETE /api/projects/[id] - Delete project
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check if project exists
    const existingProject = await db.select().from(projects).where(eq(projects.id, params.id));
    if (existingProject.length === 0) {
      return notFoundResponse('Project not found');
    }

    // Delete project (cascade will handle related records)
    await db.delete(projects).where(eq(projects.id, params.id));

    return successResponse(null, 'Project deleted successfully');
  } catch (error) {
    console.error('Error deleting project:', error);
    return errorResponse('Failed to delete project', 500);
  }
}
