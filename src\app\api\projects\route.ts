import { NextRequest } from 'next/server';
import { db, projects, projectMembers, users, folders } from '@/lib/db';
import { successResponse, errorResponse, validationErrorResponse } from '@/lib/api/response';
import { eq } from 'drizzle-orm';

// GET /api/projects - Get all projects with members
export async function GET() {
  try {
    const allProjects = await db
      .select({
        id: projects.id,
        name: projects.name,
        description: projects.description,
        color: projects.color,
        folderId: projects.folderId,
        ownerId: projects.ownerId,
        settings: projects.settings,
        createdAt: projects.createdAt,
        updatedAt: projects.updatedAt,
        owner: {
          id: users.id,
          name: users.name,
          email: users.email,
          avatar: users.avatar,
        },
        folder: {
          id: folders.id,
          name: folders.name,
          color: folders.color,
        },
      })
      .from(projects)
      .leftJoin(users, eq(projects.ownerId, users.id))
      .leftJoin(folders, eq(projects.folderId, folders.id));

    // Get members for each project
    const projectsWithMembers = await Promise.all(
      allProjects.map(async (project) => {
        const members = await db
          .select({
            userId: projectMembers.userId,
            role: projectMembers.role,
            joinedAt: projectMembers.joinedAt,
            user: {
              id: users.id,
              name: users.name,
              email: users.email,
              avatar: users.avatar,
            },
          })
          .from(projectMembers)
          .leftJoin(users, eq(projectMembers.userId, users.id))
          .where(eq(projectMembers.projectId, project.id));

        return {
          ...project,
          members,
        };
      })
    );

    return successResponse(projectsWithMembers);
  } catch (error) {
    console.error('Error fetching projects:', error);
    return errorResponse('Failed to fetch projects', 500);
  }
}

// POST /api/projects - Create a new project
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { name, description, color, folderId, ownerId, settings } = body;

    // Validation
    if (!name || !ownerId) {
      return validationErrorResponse('Name and owner ID are required');
    }

    // Create project
    const newProject = await db.insert(projects).values({
      name,
      description,
      color: color || '#3b82f6',
      folderId,
      ownerId,
      settings: settings || {
        isPublic: false,
        allowGuestAccess: false,
        defaultTaskStatus: null,
        customStatuses: [],
        timeTracking: true,
      },
    }).returning();

    // Add owner as project member
    await db.insert(projectMembers).values({
      projectId: newProject[0].id,
      userId: ownerId,
      role: 'owner',
    });

    return successResponse(newProject[0], 'Project created successfully');
  } catch (error) {
    console.error('Error creating project:', error);
    return errorResponse('Failed to create project', 500);
  }
}
