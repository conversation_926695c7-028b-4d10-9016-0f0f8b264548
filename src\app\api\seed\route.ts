import { NextRequest } from 'next/server';
import { seedDatabase } from '@/lib/db/seed';
import { successResponse, errorResponse } from '@/lib/api/response';

// POST /api/seed - Seed the database with initial data
export async function POST(request: NextRequest) {
  try {
    // Only allow seeding in development
    if (process.env.NODE_ENV === 'production') {
      return errorResponse('Seeding is not allowed in production', 403);
    }

    const result = await seedDatabase();
    return successResponse(result, 'Database seeded successfully');
  } catch (error) {
    console.error('Error seeding database:', error);
    return errorResponse('Failed to seed database', 500);
  }
}
