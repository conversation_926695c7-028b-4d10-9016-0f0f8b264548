import { NextRequest } from 'next/server';
import { db, taskStatuses } from '@/lib/db';
import { successResponse, errorResponse, validationErrorResponse } from '@/lib/api/response';
import { eq, or, isNull } from 'drizzle-orm';

// GET /api/task-statuses - Get task statuses
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const projectId = searchParams.get('projectId');

    let query = db.select().from(taskStatuses);

    if (projectId) {
      // Get statuses for specific project or global statuses
      query = query.where(or(eq(taskStatuses.projectId, projectId), isNull(taskStatuses.projectId)));
    } else {
      // Get only global statuses
      query = query.where(isNull(taskStatuses.projectId));
    }

    const statuses = await query.orderBy(taskStatuses.order);

    return successResponse(statuses);
  } catch (error) {
    console.error('Error fetching task statuses:', error);
    return errorResponse('Failed to fetch task statuses', 500);
  }
}

// POST /api/task-statuses - Create a new task status
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { name, color, order, isDefault, isCompleted, projectId } = body;

    // Validation
    if (!name || !color) {
      return validationErrorResponse('Name and color are required');
    }

    // Create task status
    const newStatus = await db.insert(taskStatuses).values({
      name,
      color,
      order: order || 0,
      isDefault: isDefault || false,
      isCompleted: isCompleted || false,
      projectId,
    }).returning();

    return successResponse(newStatus[0], 'Task status created successfully');
  } catch (error) {
    console.error('Error creating task status:', error);
    return errorResponse('Failed to create task status', 500);
  }
}
