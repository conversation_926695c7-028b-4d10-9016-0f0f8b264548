import { NextRequest } from 'next/server';
import { db, tasks, users, projects, taskStatuses, comments } from '@/lib/db';
import { successResponse, errorResponse, notFoundResponse } from '@/lib/api/response';
import { eq } from 'drizzle-orm';

// GET /api/tasks/[id] - Get task by ID with relations
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const task = await db
      .select({
        id: tasks.id,
        title: tasks.title,
        description: tasks.description,
        projectId: tasks.projectId,
        parentTaskId: tasks.parentTaskId,
        assigneeId: tasks.assigneeId,
        creatorId: tasks.creatorId,
        statusId: tasks.statusId,
        priority: tasks.priority,
        tags: tasks.tags,
        startDate: tasks.startDate,
        dueDate: tasks.dueDate,
        estimatedTime: tasks.estimatedTime,
        actualTime: tasks.actualTime,
        completedAt: tasks.completedAt,
        position: tasks.position,
        createdAt: tasks.createdAt,
        updatedAt: tasks.updatedAt,
        assignee: {
          id: users.id,
          name: users.name,
          email: users.email,
          avatar: users.avatar,
        },
        project: {
          id: projects.id,
          name: projects.name,
          color: projects.color,
        },
        status: {
          id: taskStatuses.id,
          name: taskStatuses.name,
          color: taskStatuses.color,
          isCompleted: taskStatuses.isCompleted,
        },
      })
      .from(tasks)
      .leftJoin(users, eq(tasks.assigneeId, users.id))
      .leftJoin(projects, eq(tasks.projectId, projects.id))
      .leftJoin(taskStatuses, eq(tasks.statusId, taskStatuses.id))
      .where(eq(tasks.id, params.id));

    if (task.length === 0) {
      return notFoundResponse('Task not found');
    }

    // Get task comments
    const taskComments = await db
      .select({
        id: comments.id,
        content: comments.content,
        taskId: comments.taskId,
        userId: comments.userId,
        parentCommentId: comments.parentCommentId,
        mentions: comments.mentions,
        createdAt: comments.createdAt,
        updatedAt: comments.updatedAt,
        user: {
          id: users.id,
          name: users.name,
          email: users.email,
          avatar: users.avatar,
        },
      })
      .from(comments)
      .leftJoin(users, eq(comments.userId, users.id))
      .where(eq(comments.taskId, params.id));

    const taskWithComments = {
      ...task[0],
      comments: taskComments,
    };

    return successResponse(taskWithComments);
  } catch (error) {
    console.error('Error fetching task:', error);
    return errorResponse('Failed to fetch task', 500);
  }
}

// PUT /api/tasks/[id] - Update task
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json();
    const {
      title,
      description,
      assigneeId,
      statusId,
      priority,
      tags,
      startDate,
      dueDate,
      estimatedTime,
      actualTime,
      completedAt,
      position,
    } = body;

    // Check if task exists
    const existingTask = await db.select().from(tasks).where(eq(tasks.id, params.id));
    if (existingTask.length === 0) {
      return notFoundResponse('Task not found');
    }

    // Update task
    const updatedTask = await db
      .update(tasks)
      .set({
        title,
        description,
        assigneeId,
        statusId,
        priority,
        tags,
        startDate: startDate ? new Date(startDate) : null,
        dueDate: dueDate ? new Date(dueDate) : null,
        estimatedTime,
        actualTime,
        completedAt: completedAt ? new Date(completedAt) : null,
        position,
        updatedAt: new Date(),
      })
      .where(eq(tasks.id, params.id))
      .returning();

    return successResponse(updatedTask[0], 'Task updated successfully');
  } catch (error) {
    console.error('Error updating task:', error);
    return errorResponse('Failed to update task', 500);
  }
}

// DELETE /api/tasks/[id] - Delete task
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check if task exists
    const existingTask = await db.select().from(tasks).where(eq(tasks.id, params.id));
    if (existingTask.length === 0) {
      return notFoundResponse('Task not found');
    }

    // Delete task (cascade will handle related records)
    await db.delete(tasks).where(eq(tasks.id, params.id));

    return successResponse(null, 'Task deleted successfully');
  } catch (error) {
    console.error('Error deleting task:', error);
    return errorResponse('Failed to delete task', 500);
  }
}
