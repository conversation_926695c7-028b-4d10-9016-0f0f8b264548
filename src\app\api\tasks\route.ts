import { NextRequest } from 'next/server';
import { db, tasks, users, projects, taskStatuses } from '@/lib/db';
import { successResponse, errorResponse, validationErrorResponse } from '@/lib/api/response';
import { eq, and } from 'drizzle-orm';

// GET /api/tasks - Get all tasks with relations
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const projectId = searchParams.get('projectId');
    const assigneeId = searchParams.get('assigneeId');
    const statusId = searchParams.get('statusId');

    let query = db
      .select({
        id: tasks.id,
        title: tasks.title,
        description: tasks.description,
        projectId: tasks.projectId,
        parentTaskId: tasks.parentTaskId,
        assigneeId: tasks.assigneeId,
        creatorId: tasks.creatorId,
        statusId: tasks.statusId,
        priority: tasks.priority,
        tags: tasks.tags,
        startDate: tasks.startDate,
        dueDate: tasks.dueDate,
        estimatedTime: tasks.estimatedTime,
        actualTime: tasks.actualTime,
        completedAt: tasks.completedAt,
        position: tasks.position,
        createdAt: tasks.createdAt,
        updatedAt: tasks.updatedAt,
        assignee: {
          id: users.id,
          name: users.name,
          email: users.email,
          avatar: users.avatar,
        },
        project: {
          id: projects.id,
          name: projects.name,
          color: projects.color,
        },
        status: {
          id: taskStatuses.id,
          name: taskStatuses.name,
          color: taskStatuses.color,
          isCompleted: taskStatuses.isCompleted,
        },
      })
      .from(tasks)
      .leftJoin(users, eq(tasks.assigneeId, users.id))
      .leftJoin(projects, eq(tasks.projectId, projects.id))
      .leftJoin(taskStatuses, eq(tasks.statusId, taskStatuses.id));

    // Apply filters
    const conditions = [];
    if (projectId) conditions.push(eq(tasks.projectId, projectId));
    if (assigneeId) conditions.push(eq(tasks.assigneeId, assigneeId));
    if (statusId) conditions.push(eq(tasks.statusId, statusId));

    if (conditions.length > 0) {
      query = query.where(and(...conditions));
    }

    const allTasks = await query;

    return successResponse(allTasks);
  } catch (error) {
    console.error('Error fetching tasks:', error);
    return errorResponse('Failed to fetch tasks', 500);
  }
}

// POST /api/tasks - Create a new task
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      title,
      description,
      projectId,
      parentTaskId,
      assigneeId,
      creatorId,
      statusId,
      priority,
      tags,
      startDate,
      dueDate,
      estimatedTime,
      position,
    } = body;

    // Validation
    if (!title || !projectId || !creatorId || !statusId) {
      return validationErrorResponse('Title, project ID, creator ID, and status ID are required');
    }

    // Create task
    const newTask = await db.insert(tasks).values({
      title,
      description,
      projectId,
      parentTaskId,
      assigneeId,
      creatorId,
      statusId,
      priority: priority || 'medium',
      tags: tags || [],
      startDate: startDate ? new Date(startDate) : null,
      dueDate: dueDate ? new Date(dueDate) : null,
      estimatedTime,
      position: position || 0,
    }).returning();

    return successResponse(newTask[0], 'Task created successfully');
  } catch (error) {
    console.error('Error creating task:', error);
    return errorResponse('Failed to create task', 500);
  }
}
