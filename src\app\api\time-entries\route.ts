import { NextRequest } from 'next/server';
import { db, timeEntries, tasks, users, projects } from '@/lib/db';
import { successResponse, errorResponse, validationErrorResponse } from '@/lib/api/response';
import { eq, and } from 'drizzle-orm';

// GET /api/time-entries - Get time entries
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const taskId = searchParams.get('taskId');
    const userId = searchParams.get('userId');

    let query = db
      .select({
        id: timeEntries.id,
        taskId: timeEntries.taskId,
        userId: timeEntries.userId,
        description: timeEntries.description,
        startTime: timeEntries.startTime,
        endTime: timeEntries.endTime,
        duration: timeEntries.duration,
        isRunning: timeEntries.isRunning,
        billable: timeEntries.billable,
        createdAt: timeEntries.createdAt,
        updatedAt: timeEntries.updatedAt,
        task: {
          id: tasks.id,
          title: tasks.title,
          projectId: tasks.projectId,
        },
        user: {
          id: users.id,
          name: users.name,
          email: users.email,
          avatar: users.avatar,
        },
        project: {
          id: projects.id,
          name: projects.name,
          color: projects.color,
        },
      })
      .from(timeEntries)
      .leftJoin(tasks, eq(timeEntries.taskId, tasks.id))
      .leftJoin(users, eq(timeEntries.userId, users.id))
      .leftJoin(projects, eq(tasks.projectId, projects.id));

    // Apply filters
    const conditions = [];
    if (taskId) conditions.push(eq(timeEntries.taskId, taskId));
    if (userId) conditions.push(eq(timeEntries.userId, userId));

    if (conditions.length > 0) {
      query = query.where(and(...conditions));
    }

    const entries = await query.orderBy(timeEntries.startTime);

    return successResponse(entries);
  } catch (error) {
    console.error('Error fetching time entries:', error);
    return errorResponse('Failed to fetch time entries', 500);
  }
}

// POST /api/time-entries - Create a new time entry
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { taskId, userId, description, startTime, endTime, duration, isRunning, billable } = body;

    // Validation
    if (!taskId || !userId || !startTime) {
      return validationErrorResponse('Task ID, user ID, and start time are required');
    }

    // Calculate duration if not provided
    let calculatedDuration = duration;
    if (!calculatedDuration && endTime) {
      const start = new Date(startTime);
      const end = new Date(endTime);
      calculatedDuration = Math.floor((end.getTime() - start.getTime()) / (1000 * 60)); // in minutes
    }

    // Create time entry
    const newEntry = await db.insert(timeEntries).values({
      taskId,
      userId,
      description,
      startTime: new Date(startTime),
      endTime: endTime ? new Date(endTime) : null,
      duration: calculatedDuration || 0,
      isRunning: isRunning || false,
      billable: billable !== undefined ? billable : true,
    }).returning();

    return successResponse(newEntry[0], 'Time entry created successfully');
  } catch (error) {
    console.error('Error creating time entry:', error);
    return errorResponse('Failed to create time entry', 500);
  }
}
