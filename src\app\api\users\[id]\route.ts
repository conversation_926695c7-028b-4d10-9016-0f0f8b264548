import { NextRequest } from 'next/server';
import { db, users } from '@/lib/db';
import { successResponse, errorResponse, notFoundResponse, validationErrorResponse } from '@/lib/api/response';
import { eq } from 'drizzle-orm';

// GET /api/users/[id] - Get user by ID
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await db.select().from(users).where(eq(users.id, params.id));
    
    if (user.length === 0) {
      return notFoundResponse('User not found');
    }

    return successResponse(user[0]);
  } catch (error) {
    console.error('Error fetching user:', error);
    return errorResponse('Failed to fetch user', 500);
  }
}

// PUT /api/users/[id] - Update user
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json();
    const { name, email, avatar, role } = body;

    // Check if user exists
    const existingUser = await db.select().from(users).where(eq(users.id, params.id));
    if (existingUser.length === 0) {
      return notFoundResponse('User not found');
    }

    // Update user
    const updatedUser = await db
      .update(users)
      .set({
        name,
        email,
        avatar,
        role,
        updatedAt: new Date(),
      })
      .where(eq(users.id, params.id))
      .returning();

    return successResponse(updatedUser[0], 'User updated successfully');
  } catch (error) {
    console.error('Error updating user:', error);
    return errorResponse('Failed to update user', 500);
  }
}

// DELETE /api/users/[id] - Delete user
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check if user exists
    const existingUser = await db.select().from(users).where(eq(users.id, params.id));
    if (existingUser.length === 0) {
      return notFoundResponse('User not found');
    }

    // Delete user
    await db.delete(users).where(eq(users.id, params.id));

    return successResponse(null, 'User deleted successfully');
  } catch (error) {
    console.error('Error deleting user:', error);
    return errorResponse('Failed to delete user', 500);
  }
}
