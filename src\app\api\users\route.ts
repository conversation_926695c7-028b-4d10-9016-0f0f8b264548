import { NextRequest } from 'next/server';
import { db, users } from '@/lib/db';
import { successResponse, errorResponse, validationErrorResponse } from '@/lib/api/response';
import { eq } from 'drizzle-orm';

// GET /api/users - Get all users
export async function GET() {
  try {
    const allUsers = await db.select().from(users);
    return successResponse(allUsers);
  } catch (error) {
    console.error('Error fetching users:', error);
    return errorResponse('Failed to fetch users', 500);
  }
}

// POST /api/users - Create a new user
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { name, email, avatar, role } = body;

    // Validation
    if (!name || !email) {
      return validationErrorResponse('Name and email are required');
    }

    // Check if user already exists
    const existingUser = await db.select().from(users).where(eq(users.email, email));
    if (existingUser.length > 0) {
      return validationErrorResponse('User with this email already exists');
    }

    // Create user
    const newUser = await db.insert(users).values({
      name,
      email,
      avatar,
      role: role || 'member',
    }).returning();

    return successResponse(newUser[0], 'User created successfully');
  } catch (error) {
    console.error('Error creating user:', error);
    return errorResponse('Failed to create user', 500);
  }
}
