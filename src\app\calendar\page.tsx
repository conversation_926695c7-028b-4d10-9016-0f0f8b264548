'use client';

import React, { useState } from 'react';
import { MainLayout } from '@/components/layout/main-layout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  ChevronLeftIcon,
  ChevronRightIcon,
  PlusIcon,
  CalendarDaysIcon
} from '@heroicons/react/24/outline';
import { 
  mockTasks, 
  mockCalendarEvents,
  getProjectById 
} from '@/lib/mock-data';
import { formatDate, formatTime } from '@/lib/utils';
import { format, startOfMonth, endOfMonth, eachDayOfInterval, isSameMonth, isSameDay, addMonths, subMonths } from 'date-fns';

export default function CalendarPage() {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [view, setView] = useState<'month' | 'week' | 'day'>('month');

  const monthStart = startOfMonth(currentDate);
  const monthEnd = endOfMonth(currentDate);
  const days = eachDayOfInterval({ start: monthStart, end: monthEnd });

  // Get tasks and events for the current month
  const tasksWithDates = mockTasks.filter(task => 
    task.dueDate && 
    new Date(task.dueDate) >= monthStart && 
    new Date(task.dueDate) <= monthEnd
  );

  const eventsInMonth = mockCalendarEvents.filter(event =>
    new Date(event.startTime) >= monthStart &&
    new Date(event.startTime) <= monthEnd
  );

  const getItemsForDay = (day: Date) => {
    const dayTasks = tasksWithDates.filter(task => 
      task.dueDate && isSameDay(new Date(task.dueDate), day)
    );
    const dayEvents = eventsInMonth.filter(event =>
      isSameDay(new Date(event.startTime), day)
    );
    return { tasks: dayTasks, events: dayEvents };
  };

  const navigateMonth = (direction: 'prev' | 'next') => {
    setCurrentDate(direction === 'prev' ? subMonths(currentDate, 1) : addMonths(currentDate, 1));
  };

  return (
    <MainLayout>
      <div className="flex flex-col h-full">
        {/* Header */}
        <div className="border-b bg-background px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <h1 className="text-2xl font-semibold">Calendar</h1>
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => navigateMonth('prev')}
                >
                  <ChevronLeftIcon className="h-4 w-4" />
                </Button>
                <h2 className="text-lg font-medium min-w-[200px] text-center">
                  {format(currentDate, 'MMMM yyyy')}
                </h2>
                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => navigateMonth('next')}
                >
                  <ChevronRightIcon className="h-4 w-4" />
                </Button>
              </div>
            </div>
            
            <div className="flex items-center space-x-2">
              <div className="flex items-center space-x-1">
                <Button
                  variant={view === 'month' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setView('month')}
                >
                  Month
                </Button>
                <Button
                  variant={view === 'week' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setView('week')}
                >
                  Week
                </Button>
                <Button
                  variant={view === 'day' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setView('day')}
                >
                  Day
                </Button>
              </div>
              <Button>
                <PlusIcon className="mr-2 h-4 w-4" />
                New Event
              </Button>
            </div>
          </div>
        </div>

        {/* Calendar Content */}
        <div className="flex-1 overflow-auto p-6">
          {view === 'month' && (
            <Card>
              <CardContent className="p-0">
                {/* Calendar Grid */}
                <div className="grid grid-cols-7 border-b">
                  {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map((day) => (
                    <div key={day} className="p-4 text-center font-medium text-sm text-muted-foreground border-r last:border-r-0">
                      {day}
                    </div>
                  ))}
                </div>

                <div className="grid grid-cols-7">
                  {days.map((day, dayIdx) => {
                    const { tasks, events } = getItemsForDay(day);
                    const isCurrentMonth = isSameMonth(day, currentDate);
                    const isToday = isSameDay(day, new Date());

                    return (
                      <div
                        key={day.toString()}
                        className={`min-h-[120px] p-2 border-r border-b last:border-r-0 ${
                          !isCurrentMonth ? 'bg-muted/30' : ''
                        } ${isToday ? 'bg-primary/5' : ''}`}
                      >
                        <div className="flex items-center justify-between mb-2">
                          <span
                            className={`text-sm font-medium ${
                              !isCurrentMonth 
                                ? 'text-muted-foreground' 
                                : isToday 
                                ? 'text-primary font-bold' 
                                : ''
                            }`}
                          >
                            {format(day, 'd')}
                          </span>
                        </div>

                        <div className="space-y-1">
                          {/* Events */}
                          {events.slice(0, 2).map((event) => (
                            <div
                              key={event.id}
                              className="text-xs p-1 rounded bg-blue-100 text-blue-800 truncate"
                            >
                              <div className="flex items-center space-x-1">
                                <CalendarDaysIcon className="h-3 w-3" />
                                <span>{event.title}</span>
                              </div>
                            </div>
                          ))}

                          {/* Tasks */}
                          {tasks.slice(0, 2).map((task) => {
                            const project = getProjectById(task.projectId);
                            return (
                              <div
                                key={task.id}
                                className="text-xs p-1 rounded bg-orange-100 text-orange-800 truncate"
                              >
                                <div className="flex items-center space-x-1">
                                  <div 
                                    className="h-2 w-2 rounded-full" 
                                    style={{ backgroundColor: project?.color }}
                                  />
                                  <span>{task.title}</span>
                                </div>
                              </div>
                            );
                          })}

                          {/* Show more indicator */}
                          {(tasks.length + events.length) > 4 && (
                            <div className="text-xs text-muted-foreground">
                              +{(tasks.length + events.length) - 4} more
                            </div>
                          )}
                        </div>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>
          )}

          {(view === 'week' || view === 'day') && (
            <div className="flex items-center justify-center h-64">
              <div className="text-center">
                <CalendarDaysIcon className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-medium">{view === 'week' ? 'Week' : 'Day'} View</h3>
                <p className="text-muted-foreground">
                  {view === 'week' ? 'Week' : 'Day'} view coming soon
                </p>
              </div>
            </div>
          )}
        </div>

        {/* Sidebar with upcoming items */}
        <div className="w-80 border-l bg-card p-6">
          <h3 className="font-medium mb-4">Upcoming</h3>
          
          <div className="space-y-4">
            {/* Today's tasks */}
            <div>
              <h4 className="text-sm font-medium text-muted-foreground mb-2">Today</h4>
              <div className="space-y-2">
                {tasksWithDates
                  .filter(task => task.dueDate && isSameDay(new Date(task.dueDate), new Date()))
                  .slice(0, 3)
                  .map((task) => {
                    const project = getProjectById(task.projectId);
                    return (
                      <div key={task.id} className="flex items-center space-x-2 text-sm">
                        <div 
                          className="h-2 w-2 rounded-full" 
                          style={{ backgroundColor: project?.color }}
                        />
                        <span className="flex-1 truncate">{task.title}</span>
                        <Badge variant="outline" className="text-xs">
                          {task.priority}
                        </Badge>
                      </div>
                    );
                  })}
              </div>
            </div>

            {/* Upcoming events */}
            <div>
              <h4 className="text-sm font-medium text-muted-foreground mb-2">Events</h4>
              <div className="space-y-2">
                {eventsInMonth.slice(0, 3).map((event) => (
                  <div key={event.id} className="text-sm">
                    <div className="font-medium">{event.title}</div>
                    <div className="text-xs text-muted-foreground">
                      {formatDate(event.startTime)} at {formatTime(event.startTime)}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  );
}
