'use client';

import React, { useState } from 'react';
import { MainLayout } from '@/components/layout/main-layout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { 
  CursorArrowRaysIcon,
  UsersIcon,
  CalendarIcon,
  PaperClipIcon,
  MagnifyingGlassIcon,
  DevicePhoneMobileIcon,
  CodeBracketIcon,
  CheckCircleIcon,
} from '@heroicons/react/24/outline';

// Import feature components
import { KanbanBoard } from '@/components/kanban/kanban-board';
import { PresenceIndicator } from '@/components/collaboration/presence-indicator';
import { useCollaboration } from '@/lib/realtime/collaboration';
import { useGoogleCalendar } from '@/lib/integrations/google-calendar';
import { FileUpload } from '@/components/attachments/file-upload';
import { <PERSON>Search, SearchFilter } from '@/components/search/advanced-search';
import { AIAssistant } from '@/components/ai/ai-assistant';
import { AdvancedAnalytics } from '@/components/analytics/advanced-analytics';
import { IntegrationHub } from '@/components/integrations/integration-hub';

export default function FeaturesPage() {
  const [activeDemo, setActiveDemo] = useState<string>('drag-drop');
  const [uploadedFiles, setUploadedFiles] = useState<any[]>([]);

  // Mock data for demos
  const mockUser = {
    id: 'demo-user',
    name: 'Demo User',
    avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=32&h=32&fit=crop&crop=face',
  };

  const collaboration = useCollaboration(mockUser.id, mockUser.name, mockUser.avatar);
  const googleCalendar = useGoogleCalendar();

  const features = [
    {
      id: 'drag-drop',
      title: 'Drag-and-Drop Task Management',
      description: 'Intuitive kanban boards with smooth drag-and-drop functionality',
      icon: CursorArrowRaysIcon,
      status: 'completed',
      demo: 'kanban',
    },
    {
      id: 'collaboration',
      title: 'Real-time Collaboration',
      description: 'Live presence indicators, typing status, and instant updates',
      icon: UsersIcon,
      status: 'completed',
      demo: 'presence',
    },
    {
      id: 'calendar',
      title: 'Google Calendar Integration',
      description: 'Sync tasks and deadlines with Google Calendar',
      icon: CalendarIcon,
      status: 'completed',
      demo: 'calendar',
    },
    {
      id: 'attachments',
      title: 'File Upload & Attachments',
      description: 'Drag-and-drop file uploads with preview and management',
      icon: PaperClipIcon,
      status: 'completed',
      demo: 'files',
    },
    {
      id: 'search',
      title: 'Advanced Filtering & Search',
      description: 'Powerful search with filters, saved searches, and real-time results',
      icon: MagnifyingGlassIcon,
      status: 'completed',
      demo: 'search',
    },
    {
      id: 'ai',
      title: 'AI-Powered Features',
      description: 'Groq-powered AI assistant for task suggestions and insights',
      icon: CodeBracketIcon,
      status: 'completed',
      demo: 'ai',
    },
    {
      id: 'analytics',
      title: 'Advanced Reporting & Analytics',
      description: 'Comprehensive performance metrics and team insights',
      icon: CodeBracketIcon,
      status: 'completed',
      demo: 'analytics',
    },
    {
      id: 'integrations',
      title: 'Third-party Integrations',
      description: 'Connect with Slack, GitHub, Jira, and more',
      icon: CodeBracketIcon,
      status: 'completed',
      demo: 'integrations',
    },
    {
      id: 'mobile',
      title: 'Progressive Web App',
      description: 'Mobile-optimized PWA with offline capabilities',
      icon: DevicePhoneMobileIcon,
      status: 'completed',
      demo: 'pwa',
    },
    {
      id: 'api',
      title: 'API Development & Database Integration',
      description: 'Complete REST API with PostgreSQL database',
      icon: CodeBracketIcon,
      status: 'completed',
      demo: 'api',
    },
  ];

  const mockSearchFilters: Omit<SearchFilter, 'value'>[] = [
    {
      id: 'priority',
      type: 'select',
      label: 'Priority',
      options: [
        { label: 'Low', value: 'low' },
        { label: 'Medium', value: 'medium' },
        { label: 'High', value: 'high' },
        { label: 'Urgent', value: 'urgent' },
      ],
    },
    {
      id: 'assignee',
      type: 'select',
      label: 'Assignee',
      options: [
        { label: 'John Doe', value: 'user-1' },
        { label: 'Jane Smith', value: 'user-2' },
        { label: 'Mike Johnson', value: 'user-3' },
      ],
    },
    {
      id: 'dueDate',
      type: 'date',
      label: 'Due Date',
    },
  ];

  const handleSearch = (query: string, filters: SearchFilter[]) => {
    console.log('Demo search:', { query, filters });
  };

  const handleFilesUploaded = (files: any[]) => {
    setUploadedFiles(files);
  };

  return (
    <MainLayout>
      <div className="flex flex-col h-full">
        {/* Header */}
        <div className="border-b bg-background px-6 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-semibold">Feature Showcase</h1>
              <p className="text-muted-foreground">
                Explore all the advanced features implemented in Shakil
              </p>
            </div>
            <Badge variant="outline" className="text-sm">
              All Features Implemented ✨
            </Badge>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-auto p-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Feature List */}
            <div className="lg:col-span-1">
              <Card>
                <CardHeader>
                  <CardTitle>Features</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  {features.map((feature) => {
                    const Icon = feature.icon;
                    return (
                      <div
                        key={feature.id}
                        className={`p-3 rounded-lg cursor-pointer transition-colors ${
                          activeDemo === feature.id
                            ? 'bg-primary/10 border border-primary/20'
                            : 'hover:bg-accent'
                        }`}
                        onClick={() => feature.demo && setActiveDemo(feature.id)}
                      >
                        <div className="flex items-center space-x-3">
                          <Icon className="h-5 w-5 text-primary" />
                          <div className="flex-1 min-w-0">
                            <h4 className="text-sm font-medium">{feature.title}</h4>
                            <p className="text-xs text-muted-foreground line-clamp-2">
                              {feature.description}
                            </p>
                          </div>
                          <div className="flex items-center space-x-1">
                            {feature.status === 'completed' && (
                              <CheckCircleIcon className="h-4 w-4 text-green-500" />
                            )}
                            <Badge
                              variant={feature.status === 'completed' ? 'default' : 'secondary'}
                              className="text-xs"
                            >
                              {feature.status === 'completed' ? 'Done' : 'In Progress'}
                            </Badge>
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </CardContent>
              </Card>
            </div>

            {/* Demo Area */}
            <div className="lg:col-span-2">
              <Card className="h-full">
                <CardHeader>
                  <CardTitle>
                    {features.find(f => f.id === activeDemo)?.title || 'Select a Feature'}
                  </CardTitle>
                </CardHeader>
                <CardContent className="h-full">
                  {activeDemo === 'drag-drop' && (
                    <div className="space-y-4">
                      <p className="text-sm text-muted-foreground">
                        Interactive Kanban board with drag-and-drop functionality. Try dragging tasks between columns!
                      </p>
                      <div className="h-96 border rounded-lg p-4">
                        <KanbanBoard
                          projectId="demo-project"
                          onTaskClick={(task) => console.log('Task clicked:', task)}
                          onCreateTask={() => console.log('Create task')}
                        />
                      </div>
                    </div>
                  )}

                  {activeDemo === 'collaboration' && (
                    <div className="space-y-4">
                      <p className="text-sm text-muted-foreground">
                        Real-time collaboration features with presence indicators and live updates.
                      </p>
                      <div className="space-y-4">
                        <div className="p-4 border rounded-lg">
                          <h4 className="text-sm font-medium mb-2">Presence Indicators</h4>
                          <PresenceIndicator location="demo-location" currentUserId={mockUser.id} />
                        </div>
                        <div className="p-4 border rounded-lg">
                          <h4 className="text-sm font-medium mb-2">Collaboration Stats</h4>
                          <div className="grid grid-cols-2 gap-4 text-sm">
                            <div>
                              <span className="text-muted-foreground">Active Users:</span>
                              <span className="ml-2 font-medium">{collaboration.presenceUsers.length}</span>
                            </div>
                            <div>
                              <span className="text-muted-foreground">Typing Users:</span>
                              <span className="ml-2 font-medium">{collaboration.typingUsers.length}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  {activeDemo === 'calendar' && (
                    <div className="space-y-4">
                      <p className="text-sm text-muted-foreground">
                        Google Calendar integration for syncing tasks and deadlines.
                      </p>
                      <div className="space-y-4">
                        <div className="flex items-center justify-between p-4 border rounded-lg">
                          <div>
                            <h4 className="text-sm font-medium">Calendar Status</h4>
                            <p className="text-xs text-muted-foreground">
                              {googleCalendar.isAuthenticated ? 'Connected' : 'Not connected'}
                            </p>
                          </div>
                          <Button
                            size="sm"
                            onClick={googleCalendar.isAuthenticated ? googleCalendar.disconnect : googleCalendar.authenticate}
                            disabled={googleCalendar.isLoading}
                          >
                            {googleCalendar.isLoading
                              ? 'Loading...'
                              : googleCalendar.isAuthenticated
                              ? 'Disconnect'
                              : 'Connect Calendar'
                            }
                          </Button>
                        </div>
                        {googleCalendar.isAuthenticated && (
                          <div className="p-4 border rounded-lg">
                            <h4 className="text-sm font-medium mb-2">Calendar Events</h4>
                            <p className="text-xs text-muted-foreground">
                              {googleCalendar.events.length} events loaded
                            </p>
                          </div>
                        )}
                      </div>
                    </div>
                  )}

                  {activeDemo === 'attachments' && (
                    <div className="space-y-4">
                      <p className="text-sm text-muted-foreground">
                        Drag-and-drop file upload system with preview and management.
                      </p>
                      <FileUpload
                        onFilesUploaded={handleFilesUploaded}
                        existingFiles={uploadedFiles}
                        maxFiles={5}
                        maxSize={5 * 1024 * 1024} // 5MB
                      />
                    </div>
                  )}

                  {activeDemo === 'search' && (
                    <div className="space-y-4">
                      <p className="text-sm text-muted-foreground">
                        Advanced search and filtering system with saved searches.
                      </p>
                      <AdvancedSearch
                        onSearch={handleSearch}
                        availableFilters={mockSearchFilters}
                        placeholder="Try searching for tasks..."
                      />
                    </div>
                  )}

                  {activeDemo === 'ai' && (
                    <div className="space-y-4">
                      <p className="text-sm text-muted-foreground">
                        AI-powered assistant using Groq API for intelligent task suggestions and project insights.
                      </p>
                      <AIAssistant
                        projectData={{
                          name: 'Demo Project',
                          description: 'A sample project for AI demonstration'
                        }}
                        teamData={{
                          members: ['John Doe', 'Jane Smith'],
                          tasks: 15,
                          completedTasks: 8
                        }}
                        onTaskSuggestion={(task) => console.log('AI suggested task:', task)}
                        onInsightAction={(insight) => console.log('AI insight action:', insight)}
                      />
                    </div>
                  )}

                  {activeDemo === 'analytics' && (
                    <div className="space-y-4">
                      <p className="text-sm text-muted-foreground">
                        Advanced analytics and reporting with team performance metrics.
                      </p>
                      <AdvancedAnalytics
                        data={{
                          tasks: [
                            { id: '1', title: 'Task 1', status: { isCompleted: true }, assigneeId: 'user1', createdAt: '2024-01-01', completedAt: '2024-01-05' },
                            { id: '2', title: 'Task 2', status: { isCompleted: false }, assigneeId: 'user2', createdAt: '2024-01-02', dueDate: '2024-01-20' },
                          ],
                          projects: [
                            { id: '1', name: 'Demo Project', description: 'Sample project' }
                          ],
                          users: [
                            { id: 'user1', name: 'John Doe' },
                            { id: 'user2', name: 'Jane Smith' }
                          ],
                          timeEntries: [
                            { id: '1', taskId: '1', userId: 'user1', duration: 120 },
                            { id: '2', taskId: '2', userId: 'user2', duration: 90 }
                          ]
                        }}
                        dateRange={{ start: new Date('2024-01-01'), end: new Date('2024-01-31') }}
                        onExport={(format) => console.log('Export:', format)}
                      />
                    </div>
                  )}

                  {activeDemo === 'integrations' && (
                    <div className="space-y-4">
                      <p className="text-sm text-muted-foreground">
                        Connect with third-party tools like Slack, GitHub, and Jira.
                      </p>
                      <IntegrationHub />
                    </div>
                  )}

                  {activeDemo === 'pwa' && (
                    <div className="space-y-4">
                      <p className="text-sm text-muted-foreground">
                        Progressive Web App features for mobile and offline usage.
                      </p>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="p-4 border rounded-lg">
                          <h4 className="text-sm font-medium mb-2">PWA Features</h4>
                          <ul className="text-xs space-y-1 text-muted-foreground">
                            <li>✅ Offline functionality</li>
                            <li>✅ App-like experience</li>
                            <li>✅ Push notifications</li>
                            <li>✅ Home screen installation</li>
                            <li>✅ Background sync</li>
                          </ul>
                        </div>
                        <div className="p-4 border rounded-lg">
                          <h4 className="text-sm font-medium mb-2">Mobile Optimization</h4>
                          <ul className="text-xs space-y-1 text-muted-foreground">
                            <li>✅ Responsive design</li>
                            <li>✅ Touch-friendly interface</li>
                            <li>✅ Fast loading</li>
                            <li>✅ Gesture support</li>
                            <li>✅ Adaptive layouts</li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  )}

                  {activeDemo === 'api' && (
                    <div className="space-y-4">
                      <p className="text-sm text-muted-foreground">
                        Complete REST API with PostgreSQL database integration.
                      </p>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="p-4 border rounded-lg">
                          <h4 className="text-sm font-medium mb-2">Database</h4>
                          <ul className="text-xs space-y-1 text-muted-foreground">
                            <li>✅ PostgreSQL with Neon.tech</li>
                            <li>✅ Drizzle ORM</li>
                            <li>✅ Type-safe operations</li>
                            <li>✅ Migrations & seeding</li>
                          </ul>
                        </div>
                        <div className="p-4 border rounded-lg">
                          <h4 className="text-sm font-medium mb-2">API Endpoints</h4>
                          <ul className="text-xs space-y-1 text-muted-foreground">
                            <li>✅ Users CRUD</li>
                            <li>✅ Projects CRUD</li>
                            <li>✅ Tasks CRUD</li>
                            <li>✅ Comments & Time tracking</li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  )}

                  {!activeDemo && (
                    <div className="flex items-center justify-center h-64">
                      <div className="text-center">
                        <h3 className="text-lg font-medium">Select a Feature</h3>
                        <p className="text-muted-foreground">Choose a feature from the list to see it in action</p>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  );
}
