import type { Metada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON>eist_Mono } from "next/font/google";
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Bordio - Work Management Platform",
  description: "Comprehensive work management platform for superior team collaboration and productivity",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <script
          dangerouslySetInnerHTML={{
            __html: `
              (function() {
                try {
                  // Remove browser extension attributes before React hydration
                  const observer = new MutationObserver((mutations) => {
                    for (const mutation of mutations) {
                      if (mutation.target.hasAttribute('bis_skin_checked')) {
                        mutation.target.removeAttribute('bis_skin_checked');
                      }
                      if (mutation.target.hasAttribute('bis_register')) {
                        mutation.target.removeAttribute('bis_register');
                      }
                      if (mutation.target.hasAttribute('data-gr-ext-installed')) {
                        mutation.target.removeAttribute('data-gr-ext-installed');
                      }
                      if (mutation.target.hasAttribute('crosspilot')) {
                        mutation.target.removeAttribute('crosspilot');
                      }
                    }
                  });
                  
                  observer.observe(document.documentElement, {
                    attributes: true,
                    subtree: true,
                    attributeFilter: ['bis_skin_checked', 'bis_register', 'data-gr-ext-installed', 'crosspilot']
                  });
                } catch (e) {
                  console.warn('Failed to setup hydration fix:', e);
                }
              })();
            `,
          }}
        />
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
        suppressHydrationWarning
      >
        {children}
      </body>
    </html>
  );
}
