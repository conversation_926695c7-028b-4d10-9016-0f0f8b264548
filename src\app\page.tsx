'use client';

import React, { useState, useEffect } from 'react';
import { MainLayout } from '@/components/layout/main-layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import {
  ClockIcon,
  CalendarIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  PlusIcon
} from '@heroicons/react/24/outline';
import { apiClient } from '@/lib/api/client';
import { formatRelativeDate, initials, getPriorityColor } from '@/lib/utils';
import { CreateTaskModal } from '@/components/task/create-task-modal';
import { PresenceIndicator } from '@/components/collaboration/presence-indicator';
import { useCollaboration } from '@/lib/realtime/collaboration';
import { useGoogleCalendar } from '@/lib/integrations/google-calendar';
import { AdvancedSearch, SearchFilter } from '@/components/search/advanced-search';

export default function Dashboard() {
  const [users, setUsers] = useState<any[]>([]);
  const [projects, setProjects] = useState<any[]>([]);
  const [tasks, setTasks] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [searchFilters, setSearchFilters] = useState<SearchFilter[]>([]);

  // Collaboration hooks
  const currentUser = users[0]; // For demo, use first user as current user
  const collaboration = useCollaboration(
    currentUser?.id,
    currentUser?.name,
    currentUser?.avatar
  );

  // Google Calendar integration
  const googleCalendar = useGoogleCalendar();
  const userTasks = tasks.filter(task => task.assigneeId === currentUser?.id);
  const overdueTasks = userTasks.filter(task =>
    task.dueDate && new Date(task.dueDate) < new Date() && !task.status?.isCompleted
  );
  const todayTasks = userTasks.filter(task =>
    task.dueDate &&
    new Date(task.dueDate).toDateString() === new Date().toDateString()
  );
  const completedThisWeek = userTasks.filter(task =>
    task.completedAt &&
    new Date(task.completedAt) > new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
  );

  useEffect(() => {
    const fetchData = async () => {
      try {
        const [usersData, projectsData, tasksData] = await Promise.all([
          apiClient.getUsers(),
          apiClient.getProjects(),
          apiClient.getTasks(),
        ]);

        setUsers(usersData);
        setProjects(projectsData);
        setTasks(tasksData);
      } catch (error) {
        console.error('Error fetching data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const handleTaskCreated = (newTask: any) => {
    setTasks(prev => [...prev, newTask]);
  };

  const handleSearch = (query: string, filters: SearchFilter[]) => {
    setSearchQuery(query);
    setSearchFilters(filters);
    // In a real app, this would trigger a search API call
    console.log('Search:', { query, filters });
  };

  const availableSearchFilters: Omit<SearchFilter, 'value'>[] = [
    {
      id: 'assignee',
      type: 'select',
      label: 'Assignee',
      options: users.map(user => ({ label: user.name, value: user.id })),
    },
    {
      id: 'priority',
      type: 'select',
      label: 'Priority',
      options: [
        { label: 'Low', value: 'low' },
        { label: 'Medium', value: 'medium' },
        { label: 'High', value: 'high' },
        { label: 'Urgent', value: 'urgent' },
      ],
    },
    {
      id: 'dueDate',
      type: 'date',
      label: 'Due Date',
    },
    {
      id: 'project',
      type: 'select',
      label: 'Project',
      options: projects.map(project => ({ label: project.name, value: project.id })),
    },
  ];

  if (loading) {
    return (
      <MainLayout>
        <div className="flex items-center justify-center h-full">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">Loading dashboard...</p>
          </div>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="flex flex-col h-full">
        {/* Header */}
        <div className="border-b bg-background px-6 py-4 space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-semibold">Dashboard</h1>
              <div className="flex items-center space-x-4">
                <p className="text-muted-foreground">
                  Welcome back, {currentUser?.name || 'User'}! Here's what's happening today.
                </p>
                <PresenceIndicator location="dashboard" currentUserId={currentUser?.id} />
              </div>
            </div>
            <div className="flex items-center space-x-2">
              {!googleCalendar.isAuthenticated && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={googleCalendar.authenticate}
                  disabled={googleCalendar.isLoading}
                >
                  {googleCalendar.isLoading ? 'Connecting...' : 'Connect Calendar'}
                </Button>
              )}
              <Button onClick={() => setShowCreateModal(true)}>
                <PlusIcon className="mr-2 h-4 w-4" />
                New Task
              </Button>
            </div>
          </div>

          {/* Advanced Search */}
          <AdvancedSearch
            onSearch={handleSearch}
            availableFilters={availableSearchFilters}
            placeholder="Search tasks, projects, comments..."
          />
        </div>

        {/* Content */}
        <div className="flex-1 overflow-auto p-6">
          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Tasks</CardTitle>
                <CheckCircleIcon className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{userTasks.length}</div>
                <p className="text-xs text-muted-foreground">
                  {userTasks.filter(t => t.status !== 'status-4').length} active
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Due Today</CardTitle>
                <CalendarIcon className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{todayTasks.length}</div>
                <p className="text-xs text-muted-foreground">
                  Tasks due today
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Overdue</CardTitle>
                <ExclamationTriangleIcon className="h-4 w-4 text-destructive" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-destructive">{overdueTasks.length}</div>
                <p className="text-xs text-muted-foreground">
                  Need attention
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Completed</CardTitle>
                <ClockIcon className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{completedThisWeek.length}</div>
                <p className="text-xs text-muted-foreground">
                  This week
                </p>
              </CardContent>
            </Card>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Recent Tasks */}
            <Card>
              <CardHeader>
                <CardTitle>Recent Tasks</CardTitle>
                <CardDescription>Your latest task activity</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {userTasks.slice(0, 5).map((task) => {
                    return (
                      <div key={task.id} className="flex items-center space-x-4">
                        <div
                          className="h-2 w-2 rounded-full flex-shrink-0"
                          style={{ backgroundColor: task.project?.color || '#6b7280' }}
                        />
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium truncate">{task.title}</p>
                          <p className="text-xs text-muted-foreground">{task.project?.name}</p>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Badge
                            variant="outline"
                            style={{
                              borderColor: getPriorityColor(task.priority),
                              color: getPriorityColor(task.priority)
                            }}
                          >
                            {task.priority}
                          </Badge>
                          {task.dueDate && (
                            <span className="text-xs text-muted-foreground">
                              {formatRelativeDate(task.dueDate)}
                            </span>
                          )}
                        </div>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>

            {/* Active Projects */}
            <Card>
              <CardHeader>
                <CardTitle>Active Projects</CardTitle>
                <CardDescription>Projects you're currently working on</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {projects.map((project) => {
                    const projectTasks = tasks.filter(t => t.projectId === project.id);
                    const completedTasks = projectTasks.filter(t => t.status?.isCompleted);
                    const progress = projectTasks.length > 0
                      ? Math.round((completedTasks.length / projectTasks.length) * 100)
                      : 0;

                    return (
                      <div key={project.id} className="space-y-2">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-2">
                            <div
                              className="h-3 w-3 rounded-full"
                              style={{ backgroundColor: project.color }}
                            />
                            <span className="text-sm font-medium">{project.name}</span>
                          </div>
                          <span className="text-xs text-muted-foreground">{progress}%</span>
                        </div>
                        <div className="w-full bg-secondary rounded-full h-2">
                          <div
                            className="bg-primary h-2 rounded-full transition-all"
                            style={{ width: `${progress}%` }}
                          />
                        </div>
                        <div className="flex items-center justify-between text-xs text-muted-foreground">
                          <span>{completedTasks.length} of {projectTasks.length} tasks</span>
                          <div className="flex -space-x-1">
                            {project.members?.slice(0, 3).map((member) => {
                              return member.user ? (
                                <Avatar key={member.user.id} className="h-5 w-5 border-2 border-background">
                                  <AvatarImage src={member.user.avatar} alt={member.user.name} />
                                  <AvatarFallback className="text-xs">
                                    {initials(member.user.name)}
                                  </AvatarFallback>
                                </Avatar>
                              ) : null;
                            })}
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      <CreateTaskModal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        onTaskCreated={handleTaskCreated}
      />
    </MainLayout>
  );
}
