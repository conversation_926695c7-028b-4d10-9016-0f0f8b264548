'use client';

import React, { useState, useEffect } from 'react';
import { MainLayout } from '@/components/layout/main-layout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  PlusIcon,
  ViewColumnsIcon,
  ListBulletIcon,
  CalendarIcon,
  FunnelIcon
} from '@heroicons/react/24/outline';
import { apiClient } from '@/lib/api/client';
import { cn } from '@/lib/utils';
import { KanbanBoard } from '@/components/kanban/kanban-board';
import { CreateTaskModal } from '@/components/task/create-task-modal';
import { TaskDetail } from '@/components/task/task-detail';

type ViewType = 'board' | 'list' | 'calendar';

export default function ProjectsPage() {
  const [currentView, setCurrentView] = useState<ViewType>('board');
  const [selectedProject, setSelectedProject] = useState<string | null>(null);
  const [projects, setProjects] = useState<any[]>([]);
  const [selectedTask, setSelectedTask] = useState<any>(null);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchProjects();
  }, []);

  const fetchProjects = async () => {
    try {
      const projectsData = await apiClient.getProjects() as any[];
      setProjects(projectsData);
      if (projectsData.length > 0 && !selectedProject) {
        setSelectedProject(projectsData[0].id);
      }
    } catch (error) {
      console.error('Error fetching projects:', error);
    } finally {
      setLoading(false);
    }
  };

  const project = projects.find(p => p.id === selectedProject);

  const handleTaskClick = (task: any) => {
    setSelectedTask(task);
  };

  const handleCreateTask = () => {
    setShowCreateModal(true);
  };

  const handleTaskCreated = () => {
    // Refresh data or update state as needed
    fetchProjects();
  };

  return (
    <MainLayout>
      <div className="flex flex-col h-full">
        {/* Header */}
        <div className="border-b bg-background px-6 py-4">
          <div className="flex items-center justify-between mb-4">
            <div>
              <h1 className="text-2xl font-semibold">Projects</h1>
              <p className="text-muted-foreground">
                Manage your projects and tasks
              </p>
            </div>
            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm">
                <FunnelIcon className="mr-2 h-4 w-4" />
                Filter
              </Button>
              <Button>
                <PlusIcon className="mr-2 h-4 w-4" />
                New Project
              </Button>
            </div>
          </div>

          {/* Project Tabs */}
          <div className="flex items-center space-x-1 mb-4">
            {projects.map((proj) => (
              <button
                key={proj.id}
                onClick={() => setSelectedProject(proj.id)}
                className={cn(
                  'px-3 py-1.5 text-sm font-medium rounded-md transition-colors',
                  selectedProject === proj.id
                    ? 'bg-primary text-primary-foreground'
                    : 'text-muted-foreground hover:bg-accent hover:text-accent-foreground'
                )}
              >
                <div className="flex items-center space-x-2">
                  <div
                    className="h-2 w-2 rounded-full"
                    style={{ backgroundColor: proj.color }}
                  />
                  <span>{proj.name}</span>
                </div>
              </button>
            ))}
          </div>

          {/* View Toggle */}
          <div className="flex items-center space-x-1">
            <Button
              variant={currentView === 'board' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setCurrentView('board')}
            >
              <ViewColumnsIcon className="mr-2 h-4 w-4" />
              Board
            </Button>
            <Button
              variant={currentView === 'list' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setCurrentView('list')}
            >
              <ListBulletIcon className="mr-2 h-4 w-4" />
              List
            </Button>
            <Button
              variant={currentView === 'calendar' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setCurrentView('calendar')}
            >
              <CalendarIcon className="mr-2 h-4 w-4" />
              Calendar
            </Button>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-auto p-6">
          {loading ? (
            <div className="flex items-center justify-center h-64">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>
          ) : currentView === 'board' && project ? (
            <KanbanBoard
              projectId={project.id}
              onTaskClick={handleTaskClick}
              onCreateTask={handleCreateTask}
            />
          ) : currentView === 'list' && project ? (
            <div className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Tasks</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-center py-8 text-muted-foreground">
                    <p>List view coming soon</p>
                  </div>
                </CardContent>
              </Card>
            </div>
          ) : currentView === 'calendar' ? (
            <div className="flex items-center justify-center h-64">
              <div className="text-center">
                <CalendarIcon className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-medium">Calendar View</h3>
                <p className="text-muted-foreground">Calendar integration coming soon</p>
              </div>
            </div>
          ) : (
            <div className="flex items-center justify-center h-64">
              <div className="text-center">
                <h3 className="text-lg font-medium">Select a Project</h3>
                <p className="text-muted-foreground">Choose a project to view its tasks</p>
              </div>
            </div>
          )}
        </div>

        {/* Modals */}
        {selectedTask && (
          <TaskDetail
            task={selectedTask}
            onClose={() => setSelectedTask(null)}
            onUpdate={(updatedTask) => {
              setSelectedTask(updatedTask);
              // Refresh data
              fetchProjects();
            }}
          />
        )}

        <CreateTaskModal
          isOpen={showCreateModal}
          onClose={() => setShowCreateModal(false)}
          onTaskCreated={handleTaskCreated}
          projectId={selectedProject || undefined}
        />
      </div>
    </MainLayout>
  );
}
