'use client';

import React, { useState } from 'react';
import { MainLayout } from '@/components/layout/main-layout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
  PlusIcon,
  ViewColumnsIcon,
  ListBulletIcon,
  CalendarIcon,
  FunnelIcon
} from '@heroicons/react/24/outline';
import { 
  mockProjects, 
  mockTasks, 
  mockTaskStatuses,
  getUserById,
  getProjectById 
} from '@/lib/mock-data';
import { initials, formatRelativeDate, getPriorityColor } from '@/lib/utils';
import { cn } from '@/lib/utils';

type ViewType = 'board' | 'list' | 'calendar';

export default function ProjectsPage() {
  const [currentView, setCurrentView] = useState<ViewType>('board');
  const [selectedProject, setSelectedProject] = useState<string | null>(mockProjects[0]?.id || null);

  const project = selectedProject ? getProjectById(selectedProject) : null;
  const projectTasks = selectedProject ? mockTasks.filter(task => task.projectId === selectedProject) : [];

  const tasksByStatus = mockTaskStatuses.reduce((acc, status) => {
    acc[status.id] = projectTasks.filter(task => task.status === status.id);
    return acc;
  }, {} as Record<string, typeof projectTasks>);

  return (
    <MainLayout>
      <div className="flex flex-col h-full">
        {/* Header */}
        <div className="border-b bg-background px-6 py-4">
          <div className="flex items-center justify-between mb-4">
            <div>
              <h1 className="text-2xl font-semibold">Projects</h1>
              <p className="text-muted-foreground">
                Manage your projects and tasks
              </p>
            </div>
            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm">
                <FunnelIcon className="mr-2 h-4 w-4" />
                Filter
              </Button>
              <Button>
                <PlusIcon className="mr-2 h-4 w-4" />
                New Project
              </Button>
            </div>
          </div>

          {/* Project Tabs */}
          <div className="flex items-center space-x-1 mb-4">
            {mockProjects.map((proj) => (
              <button
                key={proj.id}
                onClick={() => setSelectedProject(proj.id)}
                className={cn(
                  'px-3 py-1.5 text-sm font-medium rounded-md transition-colors',
                  selectedProject === proj.id
                    ? 'bg-primary text-primary-foreground'
                    : 'text-muted-foreground hover:bg-accent hover:text-accent-foreground'
                )}
              >
                <div className="flex items-center space-x-2">
                  <div 
                    className="h-2 w-2 rounded-full" 
                    style={{ backgroundColor: proj.color }}
                  />
                  <span>{proj.name}</span>
                </div>
              </button>
            ))}
          </div>

          {/* View Toggle */}
          <div className="flex items-center space-x-1">
            <Button
              variant={currentView === 'board' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setCurrentView('board')}
            >
              <ViewColumnsIcon className="mr-2 h-4 w-4" />
              Board
            </Button>
            <Button
              variant={currentView === 'list' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setCurrentView('list')}
            >
              <ListBulletIcon className="mr-2 h-4 w-4" />
              List
            </Button>
            <Button
              variant={currentView === 'calendar' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setCurrentView('calendar')}
            >
              <CalendarIcon className="mr-2 h-4 w-4" />
              Calendar
            </Button>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-auto p-6">
          {currentView === 'board' && project && (
            <div className="flex space-x-6 h-full">
              {mockTaskStatuses.map((status) => (
                <div key={status.id} className="flex-shrink-0 w-80">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center space-x-2">
                      <div 
                        className="h-3 w-3 rounded-full" 
                        style={{ backgroundColor: status.color }}
                      />
                      <h3 className="font-medium">{status.name}</h3>
                      <Badge variant="secondary" className="text-xs">
                        {tasksByStatus[status.id]?.length || 0}
                      </Badge>
                    </div>
                    <Button size="icon" variant="ghost" className="h-6 w-6">
                      <PlusIcon className="h-4 w-4" />
                    </Button>
                  </div>

                  <div className="space-y-3">
                    {tasksByStatus[status.id]?.map((task) => {
                      const assignee = task.assigneeId ? getUserById(task.assigneeId) : null;
                      return (
                        <Card key={task.id} className="cursor-pointer hover:shadow-md transition-shadow">
                          <CardContent className="p-4">
                            <div className="space-y-3">
                              <div>
                                <h4 className="font-medium text-sm">{task.title}</h4>
                                {task.description && (
                                  <p className="text-xs text-muted-foreground mt-1 line-clamp-2">
                                    {task.description}
                                  </p>
                                )}
                              </div>

                              {task.tags.length > 0 && (
                                <div className="flex flex-wrap gap-1">
                                  {task.tags.slice(0, 2).map((tagId) => (
                                    <Badge key={tagId} variant="outline" className="text-xs">
                                      Tag {tagId.split('-')[1]}
                                    </Badge>
                                  ))}
                                  {task.tags.length > 2 && (
                                    <Badge variant="outline" className="text-xs">
                                      +{task.tags.length - 2}
                                    </Badge>
                                  )}
                                </div>
                              )}

                              <div className="flex items-center justify-between">
                                <div className="flex items-center space-x-2">
                                  <Badge 
                                    variant="outline"
                                    className="text-xs"
                                    style={{ 
                                      borderColor: getPriorityColor(task.priority),
                                      color: getPriorityColor(task.priority)
                                    }}
                                  >
                                    {task.priority}
                                  </Badge>
                                  {task.dueDate && (
                                    <span className="text-xs text-muted-foreground">
                                      {formatRelativeDate(task.dueDate)}
                                    </span>
                                  )}
                                </div>

                                {assignee && (
                                  <Avatar className="h-6 w-6">
                                    <AvatarImage src={assignee.avatar} alt={assignee.name} />
                                    <AvatarFallback className="text-xs">
                                      {initials(assignee.name)}
                                    </AvatarFallback>
                                  </Avatar>
                                )}
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      );
                    })}
                  </div>
                </div>
              ))}
            </div>
          )}

          {currentView === 'list' && project && (
            <div className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Tasks</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {projectTasks.map((task) => {
                      const assignee = task.assigneeId ? getUserById(task.assigneeId) : null;
                      const status = mockTaskStatuses.find(s => s.id === task.status);
                      return (
                        <div key={task.id} className="flex items-center space-x-4 p-3 rounded-lg hover:bg-accent">
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center space-x-2">
                              <h4 className="font-medium text-sm">{task.title}</h4>
                              <Badge 
                                variant="outline"
                                style={{ 
                                  borderColor: status?.color,
                                  color: status?.color
                                }}
                              >
                                {status?.name}
                              </Badge>
                            </div>
                            {task.description && (
                              <p className="text-xs text-muted-foreground mt-1">
                                {task.description}
                              </p>
                            )}
                          </div>
                          <div className="flex items-center space-x-2">
                            <Badge 
                              variant="outline"
                              style={{ 
                                borderColor: getPriorityColor(task.priority),
                                color: getPriorityColor(task.priority)
                              }}
                            >
                              {task.priority}
                            </Badge>
                            {task.dueDate && (
                              <span className="text-xs text-muted-foreground">
                                {formatRelativeDate(task.dueDate)}
                              </span>
                            )}
                            {assignee && (
                              <Avatar className="h-6 w-6">
                                <AvatarImage src={assignee.avatar} alt={assignee.name} />
                                <AvatarFallback className="text-xs">
                                  {initials(assignee.name)}
                                </AvatarFallback>
                              </Avatar>
                            )}
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {currentView === 'calendar' && (
            <div className="flex items-center justify-center h-64">
              <div className="text-center">
                <CalendarIcon className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-medium">Calendar View</h3>
                <p className="text-muted-foreground">Calendar integration coming soon</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </MainLayout>
  );
}
