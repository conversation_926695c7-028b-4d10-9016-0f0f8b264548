'use client';

import React from 'react';
import { MainLayout } from '@/components/layout/main-layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  ChartBarIcon,
  ClockIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon
} from '@heroicons/react/24/outline';
import { 
  mockTasks, 
  mockTimeEntries,
  mockProjects,
  mockUsers,
  getUserById,
  getProjectById 
} from '@/lib/mock-data';
import { formatDuration, initials, getPriorityColor } from '@/lib/utils';

export default function ReportsPage() {
  // Calculate various metrics
  const totalTasks = mockTasks.length;
  const completedTasks = mockTasks.filter(task => task.status === 'status-4').length;
  const overdueTasks = mockTasks.filter(task => 
    task.dueDate && new Date(task.dueDate) < new Date() && task.status !== 'status-4'
  ).length;
  const completionRate = Math.round((completedTasks / totalTasks) * 100);

  const totalTimeLogged = mockTimeEntries.reduce((total, entry) => total + entry.duration, 0);
  const avgTimePerTask = Math.round(totalTimeLogged / totalTasks);

  // Task distribution by priority
  const tasksByPriority = mockTasks.reduce((acc, task) => {
    acc[task.priority] = (acc[task.priority] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  // Task distribution by status
  const tasksByStatus = mockTasks.reduce((acc, task) => {
    acc[task.status] = (acc[task.status] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  // User productivity
  const userProductivity = mockUsers.map(user => {
    const userTasks = mockTasks.filter(task => task.assigneeId === user.id);
    const userCompletedTasks = userTasks.filter(task => task.status === 'status-4');
    const userTimeEntries = mockTimeEntries.filter(entry => entry.userId === user.id);
    const userTotalTime = userTimeEntries.reduce((total, entry) => total + entry.duration, 0);
    
    return {
      user,
      totalTasks: userTasks.length,
      completedTasks: userCompletedTasks.length,
      completionRate: userTasks.length > 0 ? Math.round((userCompletedTasks.length / userTasks.length) * 100) : 0,
      totalTime: userTotalTime,
      avgTimePerTask: userTasks.length > 0 ? Math.round(userTotalTime / userTasks.length) : 0
    };
  });

  // Project progress
  const projectProgress = mockProjects.map(project => {
    const projectTasks = mockTasks.filter(task => task.projectId === project.id);
    const completedProjectTasks = projectTasks.filter(task => task.status === 'status-4');
    const projectTimeEntries = mockTimeEntries.filter(entry => {
      const task = mockTasks.find(t => t.id === entry.taskId);
      return task?.projectId === project.id;
    });
    const projectTotalTime = projectTimeEntries.reduce((total, entry) => total + entry.duration, 0);
    
    return {
      project,
      totalTasks: projectTasks.length,
      completedTasks: completedProjectTasks.length,
      progress: projectTasks.length > 0 ? Math.round((completedProjectTasks.length / projectTasks.length) * 100) : 0,
      totalTime: projectTotalTime
    };
  });

  return (
    <MainLayout>
      <div className="flex flex-col h-full">
        {/* Header */}
        <div className="border-b bg-background px-6 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-semibold">Reports</h1>
              <p className="text-muted-foreground">
                Analytics and insights for your projects and team
              </p>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-auto p-6">
          {/* Overview Stats */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Tasks</CardTitle>
                <CheckCircleIcon className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{totalTasks}</div>
                <p className="text-xs text-muted-foreground">
                  <span className="text-green-600">{completedTasks} completed</span>
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Completion Rate</CardTitle>
                <ArrowTrendingUpIcon className="h-4 w-4 text-green-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{completionRate}%</div>
                <p className="text-xs text-muted-foreground">
                  +2% from last week
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Overdue Tasks</CardTitle>
                <ExclamationTriangleIcon className="h-4 w-4 text-destructive" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-destructive">{overdueTasks}</div>
                <p className="text-xs text-muted-foreground">
                  Need attention
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Time Logged</CardTitle>
                <ClockIcon className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{formatDuration(totalTimeLogged)}</div>
                <p className="text-xs text-muted-foreground">
                  {formatDuration(avgTimePerTask)} avg per task
                </p>
              </CardContent>
            </Card>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
            {/* Task Distribution by Priority */}
            <Card>
              <CardHeader>
                <CardTitle>Tasks by Priority</CardTitle>
                <CardDescription>Distribution of tasks across priority levels</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {Object.entries(tasksByPriority).map(([priority, count]) => (
                    <div key={priority} className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <Badge 
                          variant="outline"
                          style={{ 
                            borderColor: getPriorityColor(priority),
                            color: getPriorityColor(priority)
                          }}
                        >
                          {priority}
                        </Badge>
                      </div>
                      <div className="flex items-center space-x-2">
                        <div className="w-24 bg-secondary rounded-full h-2">
                          <div 
                            className="h-2 rounded-full transition-all" 
                            style={{ 
                              width: `${(count / totalTasks) * 100}%`,
                              backgroundColor: getPriorityColor(priority)
                            }}
                          />
                        </div>
                        <span className="text-sm font-medium w-8 text-right">{count}</span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Project Progress */}
            <Card>
              <CardHeader>
                <CardTitle>Project Progress</CardTitle>
                <CardDescription>Completion status of active projects</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {projectProgress.map(({ project, totalTasks, completedTasks, progress, totalTime }) => (
                    <div key={project.id} className="space-y-2">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <div 
                            className="h-3 w-3 rounded-full" 
                            style={{ backgroundColor: project.color }}
                          />
                          <span className="text-sm font-medium">{project.name}</span>
                        </div>
                        <span className="text-xs text-muted-foreground">{progress}%</span>
                      </div>
                      <div className="w-full bg-secondary rounded-full h-2">
                        <div 
                          className="bg-primary h-2 rounded-full transition-all" 
                          style={{ width: `${progress}%` }}
                        />
                      </div>
                      <div className="flex items-center justify-between text-xs text-muted-foreground">
                        <span>{completedTasks} of {totalTasks} tasks</span>
                        <span>{formatDuration(totalTime)} logged</span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Team Productivity */}
          <Card>
            <CardHeader>
              <CardTitle>Team Productivity</CardTitle>
              <CardDescription>Individual team member performance metrics</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {userProductivity.map(({ user, totalTasks, completedTasks, completionRate, totalTime, avgTimePerTask }) => (
                  <div key={user.id} className="flex items-center justify-between p-4 rounded-lg border">
                    <div className="flex items-center space-x-4">
                      <Avatar className="h-10 w-10">
                        <AvatarImage src={user.avatar} alt={user.name} />
                        <AvatarFallback>{initials(user.name)}</AvatarFallback>
                      </Avatar>
                      <div>
                        <div className="font-medium">{user.name}</div>
                        <div className="text-sm text-muted-foreground">{user.email}</div>
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-4 gap-8 text-center">
                      <div>
                        <div className="text-lg font-bold">{totalTasks}</div>
                        <div className="text-xs text-muted-foreground">Total Tasks</div>
                      </div>
                      <div>
                        <div className="text-lg font-bold">{completedTasks}</div>
                        <div className="text-xs text-muted-foreground">Completed</div>
                      </div>
                      <div>
                        <div className="text-lg font-bold">{completionRate}%</div>
                        <div className="text-xs text-muted-foreground">Success Rate</div>
                      </div>
                      <div>
                        <div className="text-lg font-bold">{formatDuration(totalTime)}</div>
                        <div className="text-xs text-muted-foreground">Time Logged</div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </MainLayout>
  );
}
