'use client';

import React, { useState, useEffect } from 'react';
import { MainLayout } from '@/components/layout/main-layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import {
  ChartBarIcon,
  SparklesIcon,
  DocumentArrowDownIcon
} from '@heroicons/react/24/outline';
import { apiClient } from '@/lib/api/client';
import { AdvancedAnalytics } from '@/components/analytics/advanced-analytics';
import { AIAssistant } from '@/components/ai/ai-assistant';

export default function ReportsPage() {
  const [users, setUsers] = useState<any[]>([]);
  const [projects, setProjects] = useState<any[]>([]);
  const [tasks, setTasks] = useState<any[]>([]);
  const [timeEntries, setTimeEntries] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('analytics');

  useEffect(() => {
    const fetchData = async () => {
      try {
        const [usersData, projectsData, tasksData] = await Promise.all([
          apiClient.getUsers().catch(() => [
            { id: '1', name: 'Alice Johnson', email: '<EMAIL>', avatar: '' },
            { id: '2', name: 'Bob Smith', email: '<EMAIL>', avatar: '' },
          ]),
          apiClient.getProjects().catch(() => [
            { id: '1', name: 'Demo Project', description: 'Sample project', color: '#3b82f6' }
          ]),
          apiClient.getTasks().catch(() => [
            {
              id: '1',
              title: 'Design System Implementation',
              description: 'Create comprehensive design system',
              priority: 'high',
              assigneeId: '1',
              projectId: '1',
              status: { isCompleted: false },
            },
            {
              id: '2',
              title: 'API Integration Testing',
              description: 'Test all API endpoints',
              priority: 'urgent',
              assigneeId: '2',
              projectId: '1',
              status: { isCompleted: true },
            },
          ]),
        ]);

        setUsers(usersData as any[]);
        setProjects(projectsData as any[]);
        setTasks(tasksData as any[]);
        // Mock time entries for demo
        setTimeEntries([
          { id: '1', taskId: (tasksData as any[])[0]?.id || '1', userId: (usersData as any[])[0]?.id || '1', duration: 120 },
          { id: '2', taskId: (tasksData as any[])[1]?.id || '2', userId: (usersData as any[])[1]?.id || '2', duration: 90 },
        ]);
      } catch (error) {
        console.error('Error fetching reports data:', error);
        // Set fallback data
        setUsers([
          { id: '1', name: 'Alice Johnson', email: '<EMAIL>', avatar: '' },
          { id: '2', name: 'Bob Smith', email: '<EMAIL>', avatar: '' },
        ]);
        setProjects([{ id: '1', name: 'Demo Project', description: 'Sample project', color: '#3b82f6' }]);
        setTasks([]);
        setTimeEntries([]);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  if (loading) {
    return (
      <MainLayout>
        <div className="flex items-center justify-center h-full">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">Loading reports...</p>
          </div>
        </div>
      </MainLayout>
    );
  }

  const analyticsData = {
    tasks,
    projects,
    users,
    timeEntries,
  };

  return (
    <MainLayout>
      <div className="flex flex-col h-full">
        {/* Header */}
        <div className="border-b bg-background px-6 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-semibold">Reports & Analytics</h1>
              <p className="text-muted-foreground">
                Comprehensive insights and AI-powered analytics for your projects
              </p>
            </div>
            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm">
                <DocumentArrowDownIcon className="h-4 w-4 mr-2" />
                Export Report
              </Button>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-auto p-6">
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="analytics">
                <ChartBarIcon className="h-4 w-4 mr-2" />
                Advanced Analytics
              </TabsTrigger>
              <TabsTrigger value="ai-insights">
                <SparklesIcon className="h-4 w-4 mr-2" />
                AI Insights
              </TabsTrigger>
            </TabsList>

            <TabsContent value="analytics" className="mt-6">
              <AdvancedAnalytics
                data={analyticsData}
                dateRange={{ start: new Date('2024-01-01'), end: new Date() }}
                onExport={(format) => console.log('Export:', format)}
              />
            </TabsContent>

            <TabsContent value="ai-insights" className="mt-6">
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <div className="lg:col-span-2">
                  <Card>
                    <CardHeader>
                      <CardTitle>AI-Powered Project Insights</CardTitle>
                      <CardDescription>
                        Get intelligent recommendations and insights powered by Groq AI
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="text-center py-8">
                        <SparklesIcon className="h-12 w-12 text-primary mx-auto mb-4" />
                        <h3 className="text-lg font-medium mb-2">AI Analysis Ready</h3>
                        <p className="text-muted-foreground mb-4">
                          Use the AI Assistant to generate detailed project insights and recommendations
                        </p>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                <div className="lg:col-span-1">
                  <AIAssistant
                    projectData={projects[0]}
                    teamData={{
                      users,
                      tasks,
                      projects
                    }}
                    onTaskSuggestion={(task) => console.log('AI suggested task:', task)}
                    onInsightAction={(insight) => console.log('AI insight action:', insight)}
                  />
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </MainLayout>
  );
}
