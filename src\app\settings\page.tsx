'use client';

import React, { useState } from 'react';
import { MainLayout } from '@/components/layout/main-layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
  UserIcon,
  BellIcon,
  PaintBrushIcon,
  ShieldCheckIcon,
  GlobeAltIcon,
  ClockIcon
} from '@heroicons/react/24/outline';
import { getCurrentUser, mockTaskStatuses, generateColorPalette } from '@/lib/mock-data';
import { initials } from '@/lib/utils';

export default function SettingsPage() {
  const currentUser = getCurrentUser();
  const [activeTab, setActiveTab] = useState('profile');
  const [customStatuses, setCustomStatuses] = useState(mockTaskStatuses);

  const tabs = [
    { id: 'profile', name: 'Profile', icon: UserIcon },
    { id: 'notifications', name: 'Notifications', icon: BellIcon },
    { id: 'appearance', name: 'Appearance', icon: PaintBrushIcon },
    { id: 'workflow', name: 'Workflow', icon: ClockIcon },
    { id: 'security', name: 'Security', icon: ShieldCheckIcon },
    { id: 'integrations', name: 'Integrations', icon: GlobeAltIcon },
  ];

  const addCustomStatus = () => {
    const newStatus = {
      id: `status-${Date.now()}`,
      name: 'New Status',
      color: '#6b7280',
      order: customStatuses.length + 1
    };
    setCustomStatuses([...customStatuses, newStatus]);
  };

  const updateStatusName = (id: string, name: string) => {
    setCustomStatuses(statuses => 
      statuses.map(status => 
        status.id === id ? { ...status, name } : status
      )
    );
  };

  const updateStatusColor = (id: string, color: string) => {
    setCustomStatuses(statuses => 
      statuses.map(status => 
        status.id === id ? { ...status, color } : status
      )
    );
  };

  const removeStatus = (id: string) => {
    setCustomStatuses(statuses => statuses.filter(status => status.id !== id));
  };

  return (
    <MainLayout>
      <div className="flex flex-col h-full">
        {/* Header */}
        <div className="border-b bg-background px-6 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-semibold">Settings</h1>
              <p className="text-muted-foreground">
                Manage your account and application preferences
              </p>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-auto">
          <div className="flex">
            {/* Sidebar */}
            <div className="w-64 border-r bg-card p-6">
              <nav className="space-y-1">
                {tabs.map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                      activeTab === tab.id
                        ? 'bg-primary text-primary-foreground'
                        : 'text-muted-foreground hover:bg-accent hover:text-accent-foreground'
                    }`}
                  >
                    <tab.icon className="mr-3 h-5 w-5" />
                    {tab.name}
                  </button>
                ))}
              </nav>
            </div>

            {/* Main Content */}
            <div className="flex-1 p-6">
              {activeTab === 'profile' && (
                <div className="space-y-6">
                  <Card>
                    <CardHeader>
                      <CardTitle>Profile Information</CardTitle>
                      <CardDescription>Update your personal information and profile settings</CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="flex items-center space-x-4">
                        <Avatar className="h-20 w-20">
                          <AvatarImage src={currentUser.avatar} alt={currentUser.name} />
                          <AvatarFallback className="text-lg">{initials(currentUser.name)}</AvatarFallback>
                        </Avatar>
                        <div>
                          <Button variant="outline">Change Avatar</Button>
                          <p className="text-sm text-muted-foreground mt-1">
                            JPG, GIF or PNG. 1MB max.
                          </p>
                        </div>
                      </div>
                      
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <label className="text-sm font-medium">Full Name</label>
                          <Input defaultValue={currentUser.name} />
                        </div>
                        <div>
                          <label className="text-sm font-medium">Email</label>
                          <Input defaultValue={currentUser.email} />
                        </div>
                      </div>
                      
                      <div>
                        <label className="text-sm font-medium">Bio</label>
                        <Input placeholder="Tell us about yourself..." />
                      </div>
                      
                      <Button>Save Changes</Button>
                    </CardContent>
                  </Card>
                </div>
              )}

              {activeTab === 'notifications' && (
                <div className="space-y-6">
                  <Card>
                    <CardHeader>
                      <CardTitle>Notification Preferences</CardTitle>
                      <CardDescription>Choose how you want to be notified about updates</CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <div>
                            <div className="font-medium">Task Assignments</div>
                            <div className="text-sm text-muted-foreground">Get notified when you're assigned to a task</div>
                          </div>
                          <input type="checkbox" defaultChecked className="rounded" />
                        </div>
                        
                        <div className="flex items-center justify-between">
                          <div>
                            <div className="font-medium">Due Date Reminders</div>
                            <div className="text-sm text-muted-foreground">Receive reminders before task due dates</div>
                          </div>
                          <input type="checkbox" defaultChecked className="rounded" />
                        </div>
                        
                        <div className="flex items-center justify-between">
                          <div>
                            <div className="font-medium">Comments & Mentions</div>
                            <div className="text-sm text-muted-foreground">Get notified when someone mentions you or comments</div>
                          </div>
                          <input type="checkbox" defaultChecked className="rounded" />
                        </div>
                        
                        <div className="flex items-center justify-between">
                          <div>
                            <div className="font-medium">Project Updates</div>
                            <div className="text-sm text-muted-foreground">Receive updates about project progress</div>
                          </div>
                          <input type="checkbox" className="rounded" />
                        </div>
                      </div>
                      
                      <Button>Save Preferences</Button>
                    </CardContent>
                  </Card>
                </div>
              )}

              {activeTab === 'appearance' && (
                <div className="space-y-6">
                  <Card>
                    <CardHeader>
                      <CardTitle>Theme</CardTitle>
                      <CardDescription>Customize the appearance of your workspace</CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div>
                        <label className="text-sm font-medium">Color Theme</label>
                        <div className="grid grid-cols-3 gap-2 mt-2">
                          <div className="p-3 border rounded-lg cursor-pointer hover:bg-accent">
                            <div className="w-full h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded mb-2"></div>
                            <div className="text-sm font-medium">Default</div>
                          </div>
                          <div className="p-3 border rounded-lg cursor-pointer hover:bg-accent">
                            <div className="w-full h-8 bg-gradient-to-r from-green-500 to-teal-500 rounded mb-2"></div>
                            <div className="text-sm font-medium">Nature</div>
                          </div>
                          <div className="p-3 border rounded-lg cursor-pointer hover:bg-accent">
                            <div className="w-full h-8 bg-gradient-to-r from-orange-500 to-red-500 rounded mb-2"></div>
                            <div className="text-sm font-medium">Sunset</div>
                          </div>
                        </div>
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <div>
                          <div className="font-medium">Dark Mode</div>
                          <div className="text-sm text-muted-foreground">Use dark theme</div>
                        </div>
                        <input type="checkbox" className="rounded" />
                      </div>
                      
                      <Button>Apply Theme</Button>
                    </CardContent>
                  </Card>
                </div>
              )}

              {activeTab === 'workflow' && (
                <div className="space-y-6">
                  <Card>
                    <CardHeader>
                      <CardTitle>Custom Task Statuses</CardTitle>
                      <CardDescription>Define your own workflow stages</CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="space-y-3">
                        {customStatuses.map((status) => (
                          <div key={status.id} className="flex items-center space-x-3 p-3 border rounded-lg">
                            <div 
                              className="h-4 w-4 rounded-full cursor-pointer" 
                              style={{ backgroundColor: status.color }}
                              onClick={() => {
                                const colors = generateColorPalette();
                                const randomColor = colors[Math.floor(Math.random() * colors.length)];
                                updateStatusColor(status.id, randomColor);
                              }}
                            />
                            <Input 
                              value={status.name}
                              onChange={(e) => updateStatusName(status.id, e.target.value)}
                              className="flex-1"
                            />
                            <Badge variant={status.isDefault ? 'default' : 'outline'}>
                              {status.isDefault ? 'Default' : 'Custom'}
                            </Badge>
                            {!status.isDefault && (
                              <Button 
                                variant="ghost" 
                                size="sm"
                                onClick={() => removeStatus(status.id)}
                              >
                                Remove
                              </Button>
                            )}
                          </div>
                        ))}
                      </div>
                      
                      <Button onClick={addCustomStatus} variant="outline">
                        Add Status
                      </Button>
                      
                      <Button>Save Workflow</Button>
                    </CardContent>
                  </Card>
                </div>
              )}

              {activeTab === 'security' && (
                <div className="space-y-6">
                  <Card>
                    <CardHeader>
                      <CardTitle>Security Settings</CardTitle>
                      <CardDescription>Manage your account security and privacy</CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div>
                        <label className="text-sm font-medium">Current Password</label>
                        <Input type="password" />
                      </div>
                      
                      <div>
                        <label className="text-sm font-medium">New Password</label>
                        <Input type="password" />
                      </div>
                      
                      <div>
                        <label className="text-sm font-medium">Confirm New Password</label>
                        <Input type="password" />
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <div>
                          <div className="font-medium">Two-Factor Authentication</div>
                          <div className="text-sm text-muted-foreground">Add an extra layer of security</div>
                        </div>
                        <Button variant="outline">Enable</Button>
                      </div>
                      
                      <Button>Update Password</Button>
                    </CardContent>
                  </Card>
                </div>
              )}

              {activeTab === 'integrations' && (
                <div className="space-y-6">
                  <Card>
                    <CardHeader>
                      <CardTitle>Integrations</CardTitle>
                      <CardDescription>Connect with external services and tools</CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="space-y-4">
                        <div className="flex items-center justify-between p-4 border rounded-lg">
                          <div className="flex items-center space-x-3">
                            <div className="h-10 w-10 bg-blue-500 rounded-lg flex items-center justify-center">
                              <span className="text-white font-bold">G</span>
                            </div>
                            <div>
                              <div className="font-medium">Google Calendar</div>
                              <div className="text-sm text-muted-foreground">Sync tasks and events</div>
                            </div>
                          </div>
                          <Button variant="outline">Connect</Button>
                        </div>
                        
                        <div className="flex items-center justify-between p-4 border rounded-lg">
                          <div className="flex items-center space-x-3">
                            <div className="h-10 w-10 bg-purple-500 rounded-lg flex items-center justify-center">
                              <span className="text-white font-bold">S</span>
                            </div>
                            <div>
                              <div className="font-medium">Slack</div>
                              <div className="text-sm text-muted-foreground">Get notifications in Slack</div>
                            </div>
                          </div>
                          <Button variant="outline">Connect</Button>
                        </div>
                        
                        <div className="flex items-center justify-between p-4 border rounded-lg">
                          <div className="flex items-center space-x-3">
                            <div className="h-10 w-10 bg-gray-800 rounded-lg flex items-center justify-center">
                              <span className="text-white font-bold">G</span>
                            </div>
                            <div>
                              <div className="font-medium">GitHub</div>
                              <div className="text-sm text-muted-foreground">Link commits to tasks</div>
                            </div>
                          </div>
                          <Button variant="outline">Connect</Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  );
}
