'use client';

import React, { useState, useEffect } from 'react';
import { MainLayout } from '@/components/layout/main-layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import {
  PlayIcon,
  PauseIcon,
  StopIcon,
  ClockIcon,
  PlusIcon
} from '@heroicons/react/24/outline';
import { apiClient } from '@/lib/api/client';
import { formatDuration, formatDate } from '@/lib/utils';

export default function TimeTrackingPage() {
  const [activeTimer, setActiveTimer] = useState<string | null>(null);
  const [timerSeconds, setTimerSeconds] = useState(0);
  const [description, setDescription] = useState('');
  const [users, setUsers] = useState<any[]>([]);
  const [projects, setProjects] = useState<any[]>([]);
  const [tasks, setTasks] = useState<any[]>([]);
  const [timeEntries, setTimeEntries] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  // Mock current user - in a real app this would come from auth
  const currentUser = users[0] || { id: '1', name: 'Current User' };

  useEffect(() => {
    const fetchData = async () => {
      try {
        const [usersData, projectsData, tasksData] = await Promise.all([
          apiClient.getUsers().catch(() => [
            { id: '1', name: 'Demo User', email: '<EMAIL>', avatar: '' }
          ]),
          apiClient.getProjects().catch(() => [
            { id: '1', name: 'Demo Project', description: 'Sample project', color: '#3b82f6' }
          ]),
          apiClient.getTasks().catch(() => [
            {
              id: '1',
              title: 'Design System Implementation',
              description: 'Create comprehensive design system',
              priority: 'high',
              assigneeId: '1',
              projectId: '1',
              status: { isCompleted: false },
            },
            {
              id: '2',
              title: 'API Integration Testing',
              description: 'Test all API endpoints',
              priority: 'urgent',
              assigneeId: '1',
              projectId: '1',
              status: { isCompleted: false },
            },
          ]),
        ]);

        setUsers(usersData as any[]);
        setProjects(projectsData as any[]);
        setTasks(tasksData as any[]);

        // Mock time entries for demo
        setTimeEntries([
          {
            id: '1',
            taskId: (tasksData as any[])[0]?.id || '1',
            userId: (usersData as any[])[0]?.id || '1',
            duration: 120,
            startTime: new Date().toISOString(),
            description: 'Working on task implementation'
          },
          {
            id: '2',
            taskId: (tasksData as any[])[1]?.id || '2',
            userId: (usersData as any[])[0]?.id || '1',
            duration: 90,
            startTime: new Date(Date.now() - 86400000).toISOString(),
            description: 'Code review and testing'
          },
        ]);
      } catch (error) {
        console.error('Error fetching time tracking data:', error);
        // Set fallback data
        setUsers([{ id: '1', name: 'Demo User', email: '<EMAIL>', avatar: '' }]);
        setProjects([{ id: '1', name: 'Demo Project', description: 'Sample project', color: '#3b82f6' }]);
        setTasks([]);
        setTimeEntries([]);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  // Timer effect
  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (activeTimer) {
      interval = setInterval(() => {
        setTimerSeconds(prev => prev + 1);
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [activeTimer]);

  if (loading) {
    return (
      <MainLayout>
        <div className="flex items-center justify-center h-full">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">Loading time tracking...</p>
          </div>
        </div>
      </MainLayout>
    );
  }

  const userTimeEntries = timeEntries.filter((entry: any) => entry.userId === currentUser.id);
  const userTasks = tasks.filter((task: any) => task.assigneeId === currentUser.id);

  // Helper functions
  const getTaskById = (taskId: string) => tasks.find((task: any) => task.id === taskId);
  const getProjectById = (projectId: string) => projects.find((project: any) => project.id === projectId);

  const startTimer = (taskId: string) => {
    setActiveTimer(taskId);
    setTimerSeconds(0);
  };

  const pauseTimer = () => {
    setActiveTimer(null);
  };

  const stopTimer = () => {
    setActiveTimer(null);
    setTimerSeconds(0);
    setDescription('');
  };

  const formatTimerDisplay = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  // Calculate today's total time
  const todayEntries = userTimeEntries.filter(entry => 
    new Date(entry.startTime).toDateString() === new Date().toDateString()
  );
  const todayTotal = todayEntries.reduce((total, entry) => total + entry.duration, 0);

  // Calculate this week's total time
  const weekStart = new Date();
  weekStart.setDate(weekStart.getDate() - weekStart.getDay());
  const weekEntries = userTimeEntries.filter(entry => 
    new Date(entry.startTime) >= weekStart
  );
  const weekTotal = weekEntries.reduce((total, entry) => total + entry.duration, 0);

  return (
    <MainLayout>
      <div className="flex flex-col h-full">
        {/* Header */}
        <div className="border-b bg-background px-6 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-semibold">Time Tracking</h1>
              <p className="text-muted-foreground">
                Track time spent on tasks and projects
              </p>
            </div>
            <Button>
              <PlusIcon className="mr-2 h-4 w-4" />
              Manual Entry
            </Button>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-auto p-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Timer Section */}
            <div className="lg:col-span-2 space-y-6">
              {/* Active Timer */}
              <Card>
                <CardHeader>
                  <CardTitle>Timer</CardTitle>
                  <CardDescription>Start tracking time on your tasks</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {activeTimer ? (
                      <div className="text-center">
                        <div className="text-4xl font-mono font-bold mb-4">
                          {formatTimerDisplay(timerSeconds)}
                        </div>
                        <div className="text-sm text-muted-foreground mb-4">
                          Tracking: {getTaskById(activeTimer)?.title}
                        </div>
                        <div className="flex justify-center space-x-2">
                          <Button variant="outline" onClick={pauseTimer}>
                            <PauseIcon className="mr-2 h-4 w-4" />
                            Pause
                          </Button>
                          <Button variant="destructive" onClick={stopTimer}>
                            <StopIcon className="mr-2 h-4 w-4" />
                            Stop
                          </Button>
                        </div>
                      </div>
                    ) : (
                      <div className="text-center">
                        <div className="text-4xl font-mono font-bold mb-4 text-muted-foreground">
                          00:00:00
                        </div>
                        <p className="text-muted-foreground mb-4">
                          Select a task to start tracking time
                        </p>
                      </div>
                    )}

                    <div className="space-y-2">
                      <Input
                        placeholder="What are you working on?"
                        value={description}
                        onChange={(e) => setDescription(e.target.value)}
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Tasks to Track */}
              <Card>
                <CardHeader>
                  <CardTitle>Your Tasks</CardTitle>
                  <CardDescription>Click to start tracking time</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {userTasks.slice(0, 5).map((task) => {
                      const project = getProjectById(task.projectId);
                      const isActive = activeTimer === task.id;
                      
                      return (
                        <div
                          key={task.id}
                          className={`flex items-center justify-between p-3 rounded-lg border cursor-pointer transition-colors ${
                            isActive 
                              ? 'bg-primary/10 border-primary' 
                              : 'hover:bg-accent'
                          }`}
                          onClick={() => !isActive && startTimer(task.id)}
                        >
                          <div className="flex items-center space-x-3">
                            <div 
                              className="h-3 w-3 rounded-full" 
                              style={{ backgroundColor: project?.color }}
                            />
                            <div>
                              <div className="font-medium text-sm">{task.title}</div>
                              <div className="text-xs text-muted-foreground">{project?.name}</div>
                            </div>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Badge variant="outline">{task.priority}</Badge>
                            {isActive ? (
                              <Badge variant="default">
                                <ClockIcon className="mr-1 h-3 w-3" />
                                Active
                              </Badge>
                            ) : (
                              <Button size="sm" variant="ghost">
                                <PlayIcon className="h-4 w-4" />
                              </Button>
                            )}
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </CardContent>
              </Card>

              {/* Recent Time Entries */}
              <Card>
                <CardHeader>
                  <CardTitle>Recent Entries</CardTitle>
                  <CardDescription>Your latest time tracking entries</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {userTimeEntries.slice(0, 10).map((entry) => {
                      const task = getTaskById(entry.taskId);
                      const project = task ? getProjectById(task.projectId) : null;
                      
                      return (
                        <div key={entry.id} className="flex items-center justify-between py-2">
                          <div className="flex items-center space-x-3">
                            <div 
                              className="h-2 w-2 rounded-full" 
                              style={{ backgroundColor: project?.color }}
                            />
                            <div>
                              <div className="font-medium text-sm">{task?.title}</div>
                              <div className="text-xs text-muted-foreground">
                                {entry.description || 'No description'}
                              </div>
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="font-medium text-sm">
                              {formatDuration(entry.duration)}
                            </div>
                            <div className="text-xs text-muted-foreground">
                              {formatDate(entry.startTime)}
                            </div>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Stats Sidebar */}
            <div className="space-y-6">
              {/* Time Stats */}
              <Card>
                <CardHeader>
                  <CardTitle>Time Summary</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <div className="text-2xl font-bold">{formatDuration(todayTotal)}</div>
                    <div className="text-sm text-muted-foreground">Today</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold">{formatDuration(weekTotal)}</div>
                    <div className="text-sm text-muted-foreground">This Week</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold">
                      {formatDuration(userTimeEntries.reduce((total, entry) => total + entry.duration, 0))}
                    </div>
                    <div className="text-sm text-muted-foreground">All Time</div>
                  </div>
                </CardContent>
              </Card>

              {/* Project Breakdown */}
              <Card>
                <CardHeader>
                  <CardTitle>Project Breakdown</CardTitle>
                  <CardDescription>Time spent by project this week</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {tasks
                      .reduce((acc: any[], task: any) => {
                        const project = getProjectById(task.projectId);
                        if (!project) return acc;

                        const taskEntries = weekEntries.filter((entry: any) => entry.taskId === task.id);
                        const totalTime = taskEntries.reduce((sum: number, entry: any) => sum + entry.duration, 0);

                        if (totalTime > 0) {
                          const existing = acc.find((p: any) => p.id === project.id);
                          if (existing) {
                            existing.time += totalTime;
                          } else {
                            acc.push({ ...project, time: totalTime });
                          }
                        }

                        return acc;
                      }, [] as Array<{ id: string; name: string; color: string; time: number }>)
                      .sort((a: any, b: any) => b.time - a.time)
                      .map((project: any) => (
                        <div key={project.id} className="flex items-center justify-between">
                          <div className="flex items-center space-x-2">
                            <div 
                              className="h-3 w-3 rounded-full" 
                              style={{ backgroundColor: project.color }}
                            />
                            <span className="text-sm font-medium">{project.name}</span>
                          </div>
                          <span className="text-sm font-medium">
                            {formatDuration(project.time)}
                          </span>
                        </div>
                      ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  );
}
