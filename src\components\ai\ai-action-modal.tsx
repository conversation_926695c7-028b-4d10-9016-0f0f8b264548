'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  XMarkIcon,
  CheckCircleIcon,
  ClockIcon,
  ExclamationTriangleIcon,
  LightBulbIcon,
  PlayIcon,
  ArrowPathIcon,
} from '@heroicons/react/24/outline';
import { AIProjectInsight } from '@/lib/ai/groq-service';
import { aiAction<PERSON>and<PERSON>, AIAction, AIActionResult } from '@/lib/ai/ai-action-handler';
import { CreateTaskModal } from '@/components/task/create-task-modal';

interface AIActionModalProps {
  isOpen: boolean;
  onClose: () => void;
  insight: AIProjectInsight | null;
  projectData?: any;
  onActionCompleted?: (result: AIActionResult) => void;
}

export function AIActionModal({
  isOpen,
  onClose,
  insight,
  projectData,
  onActionCompleted,
}: AIActionModalProps) {
  const [availableActions, setAvailableActions] = useState<AIAction[]>([]);
  const [loading, setLoading] = useState(false);
  const [executingAction, setExecutingAction] = useState<string | null>(null);
  const [actionResults, setActionResults] = useState<{ [key: string]: AIActionResult }>({});
  const [showCreateTaskModal, setShowCreateTaskModal] = useState(false);
  const [selectedTaskData, setSelectedTaskData] = useState<any>(null);

  useEffect(() => {
    if (isOpen && insight) {
      generateActions();
    }
  }, [isOpen, insight]);

  const generateActions = async () => {
    if (!insight) return;

    setLoading(true);
    try {
      const result = await aiActionHandler.processInsightAction(insight, projectData);
      if (result.success && result.actions) {
        setAvailableActions(result.actions);
      }
    } catch (error) {
      console.error('Error generating actions:', error);
    } finally {
      setLoading(false);
    }
  };

  const executeAction = async (action: AIAction) => {
    setExecutingAction(action.id);
    try {
      if (action.type === 'create_task') {
        // Open task creation modal for task actions
        setSelectedTaskData(action.data);
        setShowCreateTaskModal(true);
        setExecutingAction(null);
        return;
      }

      const result = await aiActionHandler.executeAction(action, projectData);
      setActionResults(prev => ({ ...prev, [action.id]: result }));
      onActionCompleted?.(result);
    } catch (error) {
      console.error('Error executing action:', error);
      setActionResults(prev => ({
        ...prev,
        [action.id]: {
          success: false,
          message: 'Failed to execute action. Please try again.',
        },
      }));
    } finally {
      setExecutingAction(null);
    }
  };

  const handleTaskCreated = (task: any) => {
    console.log('Task created from AI action:', task);
    setShowCreateTaskModal(false);
    setSelectedTaskData(null);
    
    // Mark the action as completed
    const taskAction = availableActions.find(a => a.type === 'create_task');
    if (taskAction) {
      setActionResults(prev => ({
        ...prev,
        [taskAction.id]: {
          success: true,
          message: `Task "${task.title}" has been created successfully.`,
          data: { task },
        },
      }));
    }
  };

  const getInsightIcon = (type: string) => {
    switch (type) {
      case 'risk': return ExclamationTriangleIcon;
      case 'opportunity': return LightBulbIcon;
      case 'recommendation': return CheckCircleIcon;
      default: return LightBulbIcon;
    }
  };

  const getInsightColor = (type: string) => {
    switch (type) {
      case 'risk': return 'text-red-600 bg-red-50 border-red-200';
      case 'opportunity': return 'text-green-600 bg-green-50 border-green-200';
      case 'recommendation': return 'text-blue-600 bg-blue-50 border-blue-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getActionIcon = (type: string) => {
    switch (type) {
      case 'create_task': return CheckCircleIcon;
      case 'schedule_meeting': return ClockIcon;
      case 'send_notification': return ExclamationTriangleIcon;
      default: return PlayIcon;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'bg-red-100 text-red-800 border-red-200';
      case 'high': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'medium': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'low': return 'bg-green-100 text-green-800 border-green-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  if (!isOpen || !insight) return null;

  const IconComponent = getInsightIcon(insight.type);

  return (
    <>
      <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
        <Card className="w-full max-w-3xl max-h-[80vh] overflow-hidden">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className={`p-2 rounded-lg ${getInsightColor(insight.type)}`}>
                  <IconComponent className="h-5 w-5" />
                </div>
                <div>
                  <CardTitle className="text-lg">{insight.title}</CardTitle>
                  <p className="text-sm text-muted-foreground mt-1">
                    AI Confidence: {Math.round(insight.confidence * 100)}%
                  </p>
                </div>
              </div>
              <Button variant="ghost" size="sm" onClick={onClose}>
                <XMarkIcon className="h-4 w-4" />
              </Button>
            </div>
          </CardHeader>

          <CardContent className="space-y-6">
            {/* Insight Details */}
            <div className={`border rounded-lg p-4 ${getInsightColor(insight.type)}`}>
              <h3 className="font-medium mb-2">Insight Details</h3>
              <p className="text-sm mb-3">{insight.description}</p>
              
              {insight.actionItems && insight.actionItems.length > 0 && (
                <div>
                  <h4 className="font-medium text-sm mb-2">Recommended Actions:</h4>
                  <ul className="text-sm space-y-1">
                    {insight.actionItems.map((item, index) => (
                      <li key={index} className="flex items-center">
                        <span className="w-1.5 h-1.5 bg-current rounded-full mr-2 flex-shrink-0"></span>
                        {item}
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>

            {/* Available Actions */}
            <div>
              <h3 className="font-medium mb-3">Available Actions</h3>
              
              {loading ? (
                <div className="text-center py-8">
                  <ArrowPathIcon className="h-8 w-8 animate-spin mx-auto mb-2 text-muted-foreground" />
                  <p className="text-sm text-muted-foreground">Generating actions...</p>
                </div>
              ) : availableActions.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <PlayIcon className="h-8 w-8 mx-auto mb-2" />
                  <p className="text-sm">No specific actions available for this insight.</p>
                  <p className="text-xs">You can manually implement the recommended actions above.</p>
                </div>
              ) : (
                <div className="space-y-3">
                  {availableActions.map((action) => {
                    const ActionIcon = getActionIcon(action.type);
                    const result = actionResults[action.id];
                    const isExecuting = executingAction === action.id;
                    const isCompleted = result?.success;

                    return (
                      <div
                        key={action.id}
                        className={`border rounded-lg p-4 transition-colors ${
                          isCompleted ? 'bg-green-50 border-green-200' : 'hover:bg-accent'
                        }`}
                      >
                        <div className="flex items-start justify-between">
                          <div className="flex items-start space-x-3 flex-1">
                            <div className={`p-2 rounded-lg ${
                              isCompleted ? 'bg-green-100 text-green-600' : 'bg-muted'
                            }`}>
                              {isCompleted ? (
                                <CheckCircleIcon className="h-4 w-4" />
                              ) : (
                                <ActionIcon className="h-4 w-4" />
                              )}
                            </div>
                            
                            <div className="flex-1">
                              <h4 className="font-medium text-sm">{action.title}</h4>
                              <p className="text-xs text-muted-foreground mt-1">
                                {action.description}
                              </p>
                              
                              <div className="flex items-center space-x-2 mt-2">
                                <Badge className={getPriorityColor(action.priority)}>
                                  {action.priority}
                                </Badge>
                                {action.estimatedTime && (
                                  <span className="text-xs text-muted-foreground flex items-center">
                                    <ClockIcon className="h-3 w-3 mr-1" />
                                    {action.estimatedTime}min
                                  </span>
                                )}
                              </div>

                              {result && (
                                <div className={`mt-2 p-2 rounded text-xs ${
                                  result.success 
                                    ? 'bg-green-100 text-green-800' 
                                    : 'bg-red-100 text-red-800'
                                }`}>
                                  {result.message}
                                </div>
                              )}
                            </div>
                          </div>

                          <div className="ml-3">
                            {isCompleted ? (
                              <Badge className="bg-green-100 text-green-800">
                                Completed
                              </Badge>
                            ) : (
                              <Button
                                size="sm"
                                onClick={() => executeAction(action)}
                                disabled={isExecuting}
                              >
                                {isExecuting ? (
                                  <>
                                    <ArrowPathIcon className="h-3 w-3 mr-1 animate-spin" />
                                    Executing...
                                  </>
                                ) : (
                                  <>
                                    <PlayIcon className="h-3 w-3 mr-1" />
                                    Execute
                                  </>
                                )}
                              </Button>
                            )}
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              )}
            </div>

            {/* Action Summary */}
            {Object.keys(actionResults).length > 0 && (
              <div className="border-t pt-4">
                <h3 className="font-medium mb-2">Action Summary</h3>
                <div className="text-sm text-muted-foreground">
                  {Object.values(actionResults).filter(r => r.success).length} of{' '}
                  {Object.keys(actionResults).length} actions completed successfully.
                </div>
              </div>
            )}

            {/* Close Button */}
            <div className="flex justify-end pt-4 border-t">
              <Button onClick={onClose}>
                Close
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Task Creation Modal */}
      <CreateTaskModal
        isOpen={showCreateTaskModal}
        onClose={() => {
          setShowCreateTaskModal(false);
          setSelectedTaskData(null);
        }}
        onTaskCreated={handleTaskCreated}
        projectId={projectData?.currentProject?.id}
        prefilledData={selectedTaskData}
      />
    </>
  );
}
