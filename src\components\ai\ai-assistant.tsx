'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  SparklesIcon,
  LightBulbIcon,
  ExclamationTriangleIcon,
  ClockIcon,
  UserGroupIcon,
  DocumentTextIcon,
  ChartBarIcon,
} from '@heroicons/react/24/outline';
import { groqAI, AITaskSuggestion, AIProjectInsight, AIStandupReport } from '@/lib/ai/groq-service';

interface AIAssistantProps {
  projectData?: any;
  teamData?: any;
  onTaskSuggestion?: (task: AITaskSuggestion) => void;
  onInsightAction?: (insight: AIProjectInsight) => void;
}

export function AIAssistant({ projectData, teamData, onTaskSuggestion, onInsightAction }: AIAssistantProps) {
  const [activeTab, setActiveTab] = useState('suggestions');
  const [loading, setLoading] = useState(false);
  const [taskSuggestions, setTaskSuggestions] = useState<AITaskSuggestion[]>([]);
  const [projectInsights, setProjectInsights] = useState<AIProjectInsight[]>([]);
  const [standupReport, setStandupReport] = useState<AIStandupReport | null>(null);
  const [customPrompt, setCustomPrompt] = useState('');

  useEffect(() => {
    if (projectData) {
      loadInitialInsights();
    }
  }, [projectData]);

  const loadInitialInsights = async () => {
    setLoading(true);
    try {
      const [suggestions, insights] = await Promise.all([
        groqAI.generateTaskSuggestions(
          `Project: ${projectData?.name || 'Current Project'}\nDescription: ${projectData?.description || 'No description'}`
        ),
        groqAI.analyzeProjectRisks(projectData),
      ]);
      
      setTaskSuggestions(suggestions);
      setProjectInsights(insights);
    } catch (error) {
      console.error('Error loading AI insights:', error);
    } finally {
      setLoading(false);
    }
  };

  const generateStandup = async () => {
    setLoading(true);
    try {
      const report = await groqAI.generateStandupReport(teamData || {});
      setStandupReport(report);
    } catch (error) {
      console.error('Error generating standup:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCustomPrompt = async () => {
    if (!customPrompt.trim()) return;
    
    setLoading(true);
    try {
      // For custom prompts, we'll generate task suggestions based on the prompt
      const suggestions = await groqAI.generateTaskSuggestions(customPrompt);
      setTaskSuggestions(suggestions);
      setCustomPrompt('');
    } catch (error) {
      console.error('Error processing custom prompt:', error);
    } finally {
      setLoading(false);
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'bg-red-500';
      case 'high': return 'bg-orange-500';
      case 'medium': return 'bg-yellow-500';
      case 'low': return 'bg-green-500';
      default: return 'bg-gray-500';
    }
  };

  const getInsightIcon = (type: string) => {
    switch (type) {
      case 'risk': return ExclamationTriangleIcon;
      case 'opportunity': return LightBulbIcon;
      case 'recommendation': return SparklesIcon;
      case 'milestone': return ChartBarIcon;
      default: return DocumentTextIcon;
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center space-x-2">
          <SparklesIcon className="h-5 w-5 text-primary" />
          <CardTitle>AI Assistant</CardTitle>
          <Badge variant="outline" className="text-xs">
            Powered by Groq
          </Badge>
        </div>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="suggestions">Tasks</TabsTrigger>
            <TabsTrigger value="insights">Insights</TabsTrigger>
            <TabsTrigger value="standup">Standup</TabsTrigger>
            <TabsTrigger value="custom">Custom</TabsTrigger>
          </TabsList>

          <TabsContent value="suggestions" className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-sm font-medium">AI Task Suggestions</h3>
              <Button
                size="sm"
                onClick={loadInitialInsights}
                disabled={loading}
              >
                {loading ? 'Generating...' : 'Refresh'}
              </Button>
            </div>

            <div className="space-y-3">
              {taskSuggestions.map((suggestion, index) => (
                <Card key={index} className="p-3">
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <h4 className="text-sm font-medium">{suggestion.title}</h4>
                      <div className="flex items-center space-x-2">
                        <Badge
                          variant="outline"
                          className={`text-xs ${getPriorityColor(suggestion.priority)} text-white border-none`}
                        >
                          {suggestion.priority}
                        </Badge>
                        <div className="flex items-center text-xs text-muted-foreground">
                          <ClockIcon className="h-3 w-3 mr-1" />
                          {Math.round(suggestion.estimatedTime / 60)}h
                        </div>
                      </div>
                    </div>
                    
                    <p className="text-xs text-muted-foreground">{suggestion.description}</p>
                    
                    {suggestion.tags.length > 0 && (
                      <div className="flex flex-wrap gap-1">
                        {suggestion.tags.map((tag, tagIndex) => (
                          <Badge key={tagIndex} variant="secondary" className="text-xs">
                            {tag}
                          </Badge>
                        ))}
                      </div>
                    )}
                    
                    <Button
                      size="sm"
                      className="w-full"
                      onClick={() => onTaskSuggestion?.(suggestion)}
                    >
                      Create Task
                    </Button>
                  </div>
                </Card>
              ))}
              
              {taskSuggestions.length === 0 && !loading && (
                <div className="text-center py-8 text-muted-foreground">
                  <LightBulbIcon className="h-8 w-8 mx-auto mb-2" />
                  <p className="text-sm">No task suggestions available</p>
                  <p className="text-xs">Try refreshing or providing project context</p>
                </div>
              )}
            </div>
          </TabsContent>

          <TabsContent value="insights" className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-sm font-medium">Project Insights</h3>
              <Button
                size="sm"
                onClick={loadInitialInsights}
                disabled={loading}
              >
                {loading ? 'Analyzing...' : 'Analyze'}
              </Button>
            </div>

            <div className="space-y-3">
              {projectInsights.map((insight, index) => {
                const Icon = getInsightIcon(insight.type);
                return (
                  <Card key={index} className="p-3">
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2">
                        <Icon className="h-4 w-4 text-primary" />
                        <h4 className="text-sm font-medium">{insight.title}</h4>
                        <Badge variant="outline" className="text-xs">
                          {insight.type}
                        </Badge>
                        <Badge variant="secondary" className="text-xs">
                          {Math.round(insight.confidence * 100)}% confidence
                        </Badge>
                      </div>
                      
                      <p className="text-xs text-muted-foreground">{insight.description}</p>
                      
                      {insight.actionItems.length > 0 && (
                        <div className="space-y-1">
                          <p className="text-xs font-medium">Action Items:</p>
                          <ul className="text-xs text-muted-foreground space-y-1">
                            {insight.actionItems.map((item, itemIndex) => (
                              <li key={itemIndex} className="flex items-start">
                                <span className="mr-2">•</span>
                                <span>{item}</span>
                              </li>
                            ))}
                          </ul>
                        </div>
                      )}
                      
                      <Button
                        size="sm"
                        variant="outline"
                        className="w-full"
                        onClick={() => onInsightAction?.(insight)}
                      >
                        Take Action
                      </Button>
                    </div>
                  </Card>
                );
              })}
              
              {projectInsights.length === 0 && !loading && (
                <div className="text-center py-8 text-muted-foreground">
                  <ChartBarIcon className="h-8 w-8 mx-auto mb-2" />
                  <p className="text-sm">No insights available</p>
                  <p className="text-xs">Provide project data to get AI insights</p>
                </div>
              )}
            </div>
          </TabsContent>

          <TabsContent value="standup" className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-sm font-medium">AI Standup Report</h3>
              <Button
                size="sm"
                onClick={generateStandup}
                disabled={loading}
              >
                {loading ? 'Generating...' : 'Generate Report'}
              </Button>
            </div>

            {standupReport && (
              <Card className="p-4">
                <div className="space-y-4">
                  <div>
                    <h4 className="text-sm font-medium mb-2">Summary</h4>
                    <p className="text-sm text-muted-foreground">{standupReport.summary}</p>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <h4 className="text-sm font-medium mb-2">Completed Tasks</h4>
                      <ul className="text-xs space-y-1">
                        {standupReport.completedTasks.map((task, index) => (
                          <li key={index} className="flex items-start">
                            <span className="text-green-500 mr-2">✓</span>
                            <span>{task}</span>
                          </li>
                        ))}
                      </ul>
                    </div>

                    <div>
                      <h4 className="text-sm font-medium mb-2">Upcoming Tasks</h4>
                      <ul className="text-xs space-y-1">
                        {standupReport.upcomingTasks.map((task, index) => (
                          <li key={index} className="flex items-start">
                            <span className="text-blue-500 mr-2">→</span>
                            <span>{task}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>

                  {standupReport.blockers.length > 0 && (
                    <div>
                      <h4 className="text-sm font-medium mb-2">Blockers</h4>
                      <ul className="text-xs space-y-1">
                        {standupReport.blockers.map((blocker, index) => (
                          <li key={index} className="flex items-start">
                            <span className="text-red-500 mr-2">⚠</span>
                            <span>{blocker}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}

                  {standupReport.recommendations.length > 0 && (
                    <div>
                      <h4 className="text-sm font-medium mb-2">Recommendations</h4>
                      <ul className="text-xs space-y-1">
                        {standupReport.recommendations.map((rec, index) => (
                          <li key={index} className="flex items-start">
                            <span className="text-purple-500 mr-2">💡</span>
                            <span>{rec}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              </Card>
            )}

            {!standupReport && !loading && (
              <div className="text-center py-8 text-muted-foreground">
                <UserGroupIcon className="h-8 w-8 mx-auto mb-2" />
                <p className="text-sm">No standup report generated</p>
                <p className="text-xs">Click "Generate Report" to create an AI standup</p>
              </div>
            )}
          </TabsContent>

          <TabsContent value="custom" className="space-y-4">
            <div>
              <h3 className="text-sm font-medium mb-2">Custom AI Request</h3>
              <p className="text-xs text-muted-foreground mb-4">
                Ask the AI to help with specific project management tasks
              </p>
            </div>

            <div className="space-y-3">
              <Input
                value={customPrompt}
                onChange={(e) => setCustomPrompt(e.target.value)}
                placeholder="e.g., 'Help me plan a mobile app development project' or 'Suggest tasks for user onboarding'"
                className="text-sm"
              />
              
              <Button
                onClick={handleCustomPrompt}
                disabled={loading || !customPrompt.trim()}
                className="w-full"
              >
                {loading ? 'Processing...' : 'Ask AI'}
              </Button>
            </div>

            <div className="text-xs text-muted-foreground">
              <p className="font-medium mb-1">Example prompts:</p>
              <ul className="space-y-1">
                <li>• "Create a project plan for a new e-commerce website"</li>
                <li>• "Suggest tasks for improving team productivity"</li>
                <li>• "Help me organize a product launch timeline"</li>
                <li>• "What are the risks in my current project?"</li>
              </ul>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
