'use client';

import React, { useState, useEffect, useRef } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  SparklesIcon,
  PaperAirplaneIcon,
  XMarkIcon,
  ArrowPathIcon,
  ClockIcon,
  LightBulbIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
} from '@heroicons/react/24/outline';
import { CreateTaskModal } from '@/components/task/create-task-modal';
import { AIActionModal } from './ai-action-modal';
import { geminiAI, AITaskSuggestion, AIProjectInsight } from '@/lib/ai/gemini-service';

interface AIChatProps {
  projectData?: {
    projects: any[];
    tasks: any[];
    users: any[];
    currentProject?: any;
  };
  onTaskSuggestion?: (task: AITaskSuggestion) => void;
  onInsightAction?: (insight: AIProjectInsight) => void;
  isOpen: boolean;
  onClose: () => void;
}

interface ChatMessage {
  id: string;
  type: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
  suggestions?: AITaskSuggestion[];
  insights?: AIProjectInsight[];
  isTyping?: boolean;
}

export function AIChat({
  projectData,
  onTaskSuggestion,
  onInsightAction,
  isOpen,
  onClose,
}: AIChatProps) {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isTyping, setIsTyping] = useState(false);
  const [showCreateTaskModal, setShowCreateTaskModal] = useState(false);
  const [selectedTaskSuggestion, setSelectedTaskSuggestion] = useState<AITaskSuggestion | null>(null);
  const [showActionModal, setShowActionModal] = useState(false);
  const [selectedInsight, setSelectedInsight] = useState<AIProjectInsight | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (isOpen && messages.length === 0) {
      initializeChat();
    }
  }, [isOpen]);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const initializeChat = () => {
    const welcomeMessage: ChatMessage = {
      id: 'welcome',
      type: 'assistant',
      content: `Hi! I'm your AI assistant for Shakil AI Project Management. I can help you with:

• Creating and organizing tasks
• Analyzing project health and risks
• Optimizing team workload
• Providing productivity insights
• Scheduling and deadline management

What would you like to work on today?`,
      timestamp: new Date(),
    };
    setMessages([welcomeMessage]);
  };

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleTaskSuggestionClick = (task: AITaskSuggestion) => {
    setSelectedTaskSuggestion(task);
    setShowCreateTaskModal(true);
    onTaskSuggestion?.(task);
  };

  const handleTaskCreated = (task: any) => {
    console.log('Task created from AI chat:', task);
    setShowCreateTaskModal(false);
    setSelectedTaskSuggestion(null);
  };

  const handleCloseTaskModal = () => {
    setShowCreateTaskModal(false);
    setSelectedTaskSuggestion(null);
  };

  const handleInsightActionClick = (insight: AIProjectInsight) => {
    setSelectedInsight(insight);
    setShowActionModal(true);
    onInsightAction?.(insight);
  };

  const handleActionCompleted = (result: any) => {
    console.log('AI action completed in chat:', result);
  };

  const handleCloseActionModal = () => {
    setShowActionModal(false);
    setSelectedInsight(null);
  };

  const handleSendMessage = async () => {
    if (!inputValue.trim() || isLoading) return;

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      type: 'user',
      content: inputValue,
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsLoading(true);
    setIsTyping(true);

    // Add typing indicator
    const typingMessage: ChatMessage = {
      id: 'typing',
      type: 'assistant',
      content: '',
      timestamp: new Date(),
      isTyping: true,
    };
    setMessages(prev => [...prev, typingMessage]);

    try {
      const response = await generateAIResponse(inputValue, projectData);
      
      // Remove typing indicator and add actual response
      setMessages(prev => prev.filter(msg => msg.id !== 'typing'));
      
      const assistantMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        type: 'assistant',
        content: response.content,
        timestamp: new Date(),
        suggestions: response.suggestions,
        insights: response.insights,
      };

      setMessages(prev => [...prev, assistantMessage]);
    } catch (error) {
      console.error('Error generating AI response:', error);
      setMessages(prev => prev.filter(msg => msg.id !== 'typing'));
      
      const errorMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        type: 'assistant',
        content: "I apologize, but I'm having trouble processing your request right now. Please try again or ask me something else about your project management needs.",
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
      setIsTyping(false);
    }
  };

  const generateAIResponse = async (input: string, data?: any) => {
    const lowerInput = input.toLowerCase();
    
    // Analyze input and generate contextual response
    if (lowerInput.includes('task') || lowerInput.includes('create') || lowerInput.includes('add')) {
      const suggestions = await generateTaskSuggestions(data);
      return {
        content: "I can help you create tasks! Based on your project context, here are some suggestions that might be relevant:",
        suggestions: suggestions.slice(0, 3),
      };
    }
    
    if (lowerInput.includes('insight') || lowerInput.includes('analyze') || lowerInput.includes('health') || lowerInput.includes('risk')) {
      const insights = await generateProjectInsights(data);
      return {
        content: "Here's my analysis of your project health and some insights that might help improve your team's productivity:",
        insights: insights.slice(0, 2),
      };
    }
    
    if (lowerInput.includes('time') || lowerInput.includes('schedule') || lowerInput.includes('deadline')) {
      const overdueTasks = data?.tasks?.filter(t => t.dueDate && new Date(t.dueDate) < new Date() && !t.status?.isCompleted) || [];
      return {
        content: `I can help with time management! ${overdueTasks.length > 0 ? `You have ${overdueTasks.length} overdue tasks that need attention.` : 'Your deadlines look manageable.'} Here are some time management suggestions:

• Review and prioritize overdue tasks
• Set realistic deadlines for new tasks
• Consider breaking large tasks into smaller ones
• Use time blocking for focused work sessions

Would you like me to analyze your current workload distribution?`,
      };
    }
    
    if (lowerInput.includes('team') || lowerInput.includes('assign') || lowerInput.includes('workload')) {
      const teamSize = data?.users?.length || 0;
      const activeTasks = data?.tasks?.filter(t => !t.status?.isCompleted) || [];
      return {
        content: `Your team has ${teamSize} members with ${activeTasks.length} active tasks. Here's what I can help with:

• Analyze workload distribution across team members
• Suggest optimal task assignments based on skills
• Identify potential bottlenecks or overloaded team members
• Recommend workload balancing strategies

Would you like me to analyze the current workload balance?`,
      };
    }

    if (lowerInput.includes('help') || lowerInput.includes('what can you do')) {
      return {
        content: `I'm here to help you manage your projects more effectively! Here's what I can do:

**Task Management:**
• Suggest new tasks based on project context
• Help prioritize existing tasks
• Estimate task completion times

**Project Analysis:**
• Identify project risks and opportunities
• Analyze team productivity patterns
• Provide project health insights

**Team Optimization:**
• Suggest optimal task assignments
• Analyze workload distribution
• Recommend productivity improvements

**Planning & Scheduling:**
• Help with project timeline planning
• Suggest deadline optimizations
• Identify scheduling conflicts

Just ask me about any of these areas, and I'll provide specific, actionable advice!`,
      };
    }
    
    // Default response with context
    const taskCount = data?.tasks?.length || 0;
    const projectName = data?.currentProject?.name || 'your project';
    
    return {
      content: `I'm here to help with ${projectName}! With ${taskCount} tasks in your project, I can assist with:

• **Task Creation** - "Help me create tasks for user authentication"
• **Project Analysis** - "Analyze my project health and risks"
• **Time Management** - "How can I optimize my team's schedule?"
• **Team Coordination** - "Suggest task assignments for my team"

What specific area would you like to focus on?`,
    };
  };

  const generateTaskSuggestions = async (data?: any): Promise<AITaskSuggestion[]> => {
    try {
      const context = data ? `
        Project: ${data.currentProject?.name || 'Current Project'}
        Tasks: ${data.tasks?.length || 0} total
        Team: ${data.users?.length || 0} members
      ` : 'General project context';
      
      return await geminiAI.generateTaskSuggestions(context);
    } catch (error) {
      console.error('Error generating task suggestions:', error);
      return [
        {
          title: 'Review Project Progress',
          description: 'Conduct a comprehensive review of current project status',
          priority: 'medium',
          estimatedTime: 120,
          tags: ['review', 'planning'],
        },
        {
          title: 'Update Documentation',
          description: 'Ensure all project documentation is current and accessible',
          priority: 'low',
          estimatedTime: 60,
          tags: ['documentation'],
        },
      ];
    }
  };

  const generateProjectInsights = async (data?: any): Promise<AIProjectInsight[]> => {
    try {
      return await geminiAI.analyzeProjectRisks(data || {});
    } catch (error) {
      console.error('Error generating insights:', error);
      const tasks = data?.tasks || [];
      const overdueTasks = tasks.filter(t => t.dueDate && new Date(t.dueDate) < new Date() && !t.status?.isCompleted);
      
      return [
        {
          type: overdueTasks.length > 0 ? 'risk' : 'recommendation',
          title: overdueTasks.length > 0 ? 'Overdue Tasks Detected' : 'Project Health Check',
          description: overdueTasks.length > 0 
            ? `${overdueTasks.length} tasks are past their due date`
            : 'Your project appears to be on track',
          confidence: 0.8,
          actionItems: overdueTasks.length > 0 
            ? ['Review overdue tasks', 'Reschedule deadlines', 'Reassign if needed']
            : ['Continue monitoring progress', 'Plan upcoming milestones'],
          impact: overdueTasks.length > 0 ? 'high' : 'medium',
        },
      ];
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'bg-red-100 text-red-800 border-red-200';
      case 'high': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'medium': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'low': return 'bg-green-100 text-green-800 border-green-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getInsightIcon = (type: string) => {
    switch (type) {
      case 'risk': return ExclamationTriangleIcon;
      case 'opportunity': return LightBulbIcon;
      case 'recommendation': return CheckCircleIcon;
      default: return LightBulbIcon;
    }
  };

  const getInsightColor = (type: string) => {
    switch (type) {
      case 'risk': return 'text-red-600 bg-red-50 border-red-200';
      case 'opportunity': return 'text-green-600 bg-green-50 border-green-200';
      case 'recommendation': return 'text-blue-600 bg-blue-50 border-blue-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <Card className="w-full max-w-2xl h-[600px] flex flex-col">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                <SparklesIcon className="h-4 w-4 text-white" />
              </div>
              <CardTitle className="text-lg">AI Assistant Chat</CardTitle>
              <Badge variant="outline" className="text-xs">
                Powered by Gemini
              </Badge>
            </div>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <XMarkIcon className="h-4 w-4" />
            </Button>
          </div>
        </CardHeader>

        <CardContent className="flex-1 flex flex-col p-4">
          {/* Messages */}
          <div className="flex-1 overflow-y-auto space-y-4 mb-4">
            {messages.map((message) => (
              <div key={message.id} className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}>
                <div className={`max-w-[80%] ${message.type === 'user' ? 'order-2' : 'order-1'}`}>
                  {message.isTyping ? (
                    <div className="bg-muted rounded-lg px-3 py-2 flex items-center space-x-2">
                      <ArrowPathIcon className="h-4 w-4 animate-spin" />
                      <span className="text-sm">AI is thinking...</span>
                    </div>
                  ) : (
                    <>
                      <div className={`rounded-lg px-3 py-2 ${
                        message.type === 'user' 
                          ? 'bg-primary text-primary-foreground' 
                          : 'bg-muted'
                      }`}>
                        <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                        <p className="text-xs opacity-70 mt-1">{formatTime(message.timestamp)}</p>
                      </div>
                      
                      {/* Task Suggestions */}
                      {message.suggestions && message.suggestions.length > 0 && (
                        <div className="mt-2 space-y-2">
                          {message.suggestions.map((suggestion, index) => (
                            <div key={index} className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                              <div className="flex items-start justify-between">
                                <div className="flex-1">
                                  <h4 className="font-medium text-sm">{suggestion.title}</h4>
                                  <p className="text-xs text-muted-foreground mt-1">{suggestion.description}</p>
                                  <div className="flex items-center space-x-2 mt-2">
                                    <Badge className={getPriorityColor(suggestion.priority)}>
                                      {suggestion.priority}
                                    </Badge>
                                    <span className="text-xs text-muted-foreground flex items-center">
                                      <ClockIcon className="h-3 w-3 mr-1" />
                                      {Math.round(suggestion.estimatedTime / 60)}h
                                    </span>
                                  </div>
                                </div>
                                <Button
                                  size="sm"
                                  onClick={() => handleTaskSuggestionClick(suggestion)}
                                  className="ml-2"
                                >
                                  Create
                                </Button>
                              </div>
                            </div>
                          ))}
                        </div>
                      )}
                      
                      {/* Project Insights */}
                      {message.insights && message.insights.length > 0 && (
                        <div className="mt-2 space-y-2">
                          {message.insights.map((insight, index) => {
                            const IconComponent = getInsightIcon(insight.type);
                            return (
                              <div key={index} className={`border rounded-lg p-3 ${getInsightColor(insight.type)}`}>
                                <div className="flex items-start space-x-2">
                                  <IconComponent className="h-4 w-4 mt-0.5 flex-shrink-0" />
                                  <div className="flex-1">
                                    <h4 className="font-medium text-sm">{insight.title}</h4>
                                    <p className="text-xs mt-1">{insight.description}</p>
                                    {insight.actionItems && insight.actionItems.length > 0 && (
                                      <ul className="text-xs mt-2 space-y-1">
                                        {insight.actionItems.map((item, i) => (
                                          <li key={i} className="flex items-center">
                                            <span className="w-1 h-1 bg-current rounded-full mr-2"></span>
                                            {item}
                                          </li>
                                        ))}
                                      </ul>
                                    )}
                                  </div>
                                  <Button
                                    size="sm"
                                    variant="outline"
                                    onClick={() => handleInsightActionClick(insight)}
                                  >
                                    Take Action
                                  </Button>
                                </div>
                              </div>
                            );
                          })}
                        </div>
                      )}
                    </>
                  )}
                </div>
                
                {message.type === 'assistant' && !message.isTyping && (
                  <Avatar className="w-6 h-6 order-1 mr-2">
                    <AvatarFallback className="bg-gradient-to-r from-blue-500 to-purple-600 text-white text-xs">
                      AI
                    </AvatarFallback>
                  </Avatar>
                )}
              </div>
            ))}
            
            <div ref={messagesEndRef} />
          </div>

          {/* Input */}
          <div className="flex space-x-2">
            <Input
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Ask me anything about your project..."
              disabled={isLoading}
              className="flex-1"
            />
            <Button
              onClick={handleSendMessage}
              disabled={isLoading || !inputValue.trim()}
              size="sm"
            >
              <PaperAirplaneIcon className="h-4 w-4" />
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Task Creation Modal */}
      <CreateTaskModal
        isOpen={showCreateTaskModal}
        onClose={handleCloseTaskModal}
        onTaskCreated={handleTaskCreated}
        projectId={projectData?.currentProject?.id}
        prefilledData={selectedTaskSuggestion ? {
          title: selectedTaskSuggestion.title,
          description: selectedTaskSuggestion.description,
          priority: selectedTaskSuggestion.priority,
          estimatedTime: selectedTaskSuggestion.estimatedTime,
        } : undefined}
      />

      {/* AI Action Modal */}
      <AIActionModal
        isOpen={showActionModal}
        onClose={handleCloseActionModal}
        insight={selectedInsight}
        projectData={projectData}
        onActionCompleted={handleActionCompleted}
      />
    </div>
  );
}
