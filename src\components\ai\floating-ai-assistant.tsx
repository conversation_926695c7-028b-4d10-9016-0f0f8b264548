'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  SparklesIcon,
  ChatBubbleLeftRightIcon,
  XMarkIcon,
} from '@heroicons/react/24/outline';
import { AIChat } from './ai-chat';
import { AIAssistant } from './ai-assistant';
import { CreateTaskModal } from '@/components/task/create-task-modal';
import { AITaskSuggestion, AIProjectInsight } from '@/lib/ai/groq-service';

interface FloatingAIAssistantProps {
  projectData?: {
    projects: any[];
    tasks: any[];
    users: any[];
    currentProject?: any;
  };
  onTaskSuggestion?: (task: AITaskSuggestion) => void;
  onInsightAction?: (insight: AIProjectInsight) => void;
  position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left';
  mode?: 'chat' | 'assistant' | 'both';
}

export function FloatingAIAssistant({
  projectData,
  onTaskSuggestion,
  onInsightAction,
  position = 'bottom-right',
  mode = 'both',
}: FloatingAIAssistantProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [activeMode, setActiveMode] = useState<'chat' | 'assistant'>('chat');
  const [hasNewSuggestions, setHasNewSuggestions] = useState(false);
  const [isMinimized, setIsMinimized] = useState(false);
  const [showCreateTaskModal, setShowCreateTaskModal] = useState(false);
  const [selectedTaskSuggestion, setSelectedTaskSuggestion] = useState<AITaskSuggestion | null>(null);

  useEffect(() => {
    // Simulate new suggestions notification
    const timer = setTimeout(() => {
      if (!isOpen && projectData?.tasks && projectData.tasks.length > 0) {
        setHasNewSuggestions(true);
      }
    }, 5000);

    return () => clearTimeout(timer);
  }, [isOpen, projectData]);

  const getPositionClasses = () => {
    switch (position) {
      case 'bottom-right':
        return 'bottom-4 right-4';
      case 'bottom-left':
        return 'bottom-4 left-4';
      case 'top-right':
        return 'top-4 right-4';
      case 'top-left':
        return 'top-4 left-4';
      default:
        return 'bottom-4 right-4';
    }
  };

  const handleOpen = () => {
    setIsOpen(true);
    setHasNewSuggestions(false);
  };

  const handleClose = () => {
    setIsOpen(false);
    setIsMinimized(false);
  };

  const handleTaskSuggestion = (task: AITaskSuggestion) => {
    setSelectedTaskSuggestion(task);
    setShowCreateTaskModal(true);
    onTaskSuggestion?.(task);
  };

  const handleInsightAction = (insight: AIProjectInsight) => {
    onInsightAction?.(insight);
    // Optionally close the assistant after taking action
    // setIsOpen(false);
  };

  const handleTaskCreated = (task: any) => {
    console.log('Task created from AI suggestion:', task);
    setShowCreateTaskModal(false);
    setSelectedTaskSuggestion(null);
    // Optionally close the AI assistant
    // setIsOpen(false);
  };

  const handleCloseTaskModal = () => {
    setShowCreateTaskModal(false);
    setSelectedTaskSuggestion(null);
  };

  // Floating button when closed
  if (!isOpen) {
    return (
      <div className={`fixed ${getPositionClasses()} z-50`}>
        <div className="relative">
          <Button
            onClick={handleOpen}
            className="rounded-full w-14 h-14 shadow-lg bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 border-0"
            size="sm"
          >
            <SparklesIcon className="h-6 w-6 text-white" />
          </Button>
          
          {hasNewSuggestions && (
            <div className="absolute -top-1 -right-1">
              <Badge className="bg-red-500 text-white text-xs px-1.5 py-0.5 rounded-full animate-pulse">
                New
              </Badge>
            </div>
          )}
          
          {/* Tooltip */}
          <div className="absolute bottom-full right-0 mb-2 opacity-0 hover:opacity-100 transition-opacity pointer-events-none">
            <div className="bg-black text-white text-xs rounded px-2 py-1 whitespace-nowrap">
              AI Assistant - Click for help
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Expanded assistant interface
  return (
    <>
      {activeMode === 'chat' && (
        <AIChat
          projectData={projectData}
          onTaskSuggestion={handleTaskSuggestion}
          onInsightAction={handleInsightAction}
          isOpen={isOpen}
          onClose={handleClose}
        />
      )}
      
      {activeMode === 'assistant' && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className="w-full max-w-4xl h-[600px] bg-background rounded-lg shadow-xl">
            <div className="flex items-center justify-between p-4 border-b">
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                  <SparklesIcon className="h-4 w-4 text-white" />
                </div>
                <h2 className="text-lg font-semibold">AI Assistant</h2>
                <Badge variant="outline" className="text-xs">
                  Powered by Groq
                </Badge>
              </div>
              
              <div className="flex items-center space-x-2">
                {mode === 'both' && (
                  <div className="flex bg-muted rounded-lg p-1">
                    <button
                      onClick={() => setActiveMode('chat')}
                      className={`px-3 py-1 text-sm rounded-md transition-colors ${
                        activeMode === 'chat' ? 'bg-background shadow-sm' : 'hover:bg-background/50'
                      }`}
                    >
                      <ChatBubbleLeftRightIcon className="h-4 w-4 mr-1 inline" />
                      Chat
                    </button>
                    <button
                      onClick={() => setActiveMode('assistant')}
                      className={`px-3 py-1 text-sm rounded-md transition-colors ${
                        activeMode === 'assistant' ? 'bg-background shadow-sm' : 'hover:bg-background/50'
                      }`}
                    >
                      <SparklesIcon className="h-4 w-4 mr-1 inline" />
                      Assistant
                    </button>
                  </div>
                )}
                
                <Button variant="ghost" size="sm" onClick={handleClose}>
                  <XMarkIcon className="h-4 w-4" />
                </Button>
              </div>
            </div>
            
            <div className="h-[calc(600px-73px)] overflow-hidden">
              <AIAssistant
                projectData={projectData}
                teamData={{
                  users: projectData?.users || [],
                  tasks: projectData?.tasks || [],
                  projects: projectData?.projects || [],
                }}
                onTaskSuggestion={handleTaskSuggestion}
                onInsightAction={handleInsightAction}
              />
            </div>
          </div>
        </div>
      )}

      {/* Task Creation Modal */}
      <CreateTaskModal
        isOpen={showCreateTaskModal}
        onClose={handleCloseTaskModal}
        onTaskCreated={handleTaskCreated}
        projectId={projectData?.currentProject?.id}
        prefilledData={selectedTaskSuggestion ? {
          title: selectedTaskSuggestion.title,
          description: selectedTaskSuggestion.description,
          priority: selectedTaskSuggestion.priority,
          estimatedTime: selectedTaskSuggestion.estimatedTime,
        } : undefined}
      />
    </>
  );
}

// Quick access AI suggestions component for specific contexts
export function QuickAISuggestions({
  context,
  projectData,
  onTaskSuggestion,
  className = '',
}: {
  context: 'dashboard' | 'project' | 'task-list' | 'calendar';
  projectData?: any;
  onTaskSuggestion?: (task: AITaskSuggestion) => void;
  className?: string;
}) {
  const [suggestions, setSuggestions] = useState<AITaskSuggestion[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showCreateTaskModal, setShowCreateTaskModal] = useState(false);
  const [selectedTaskSuggestion, setSelectedTaskSuggestion] = useState<AITaskSuggestion | null>(null);

  useEffect(() => {
    generateContextualSuggestions();
  }, [context, projectData]);

  const generateContextualSuggestions = async () => {
    setIsLoading(true);
    try {
      // Generate context-specific suggestions
      const contextPrompts = {
        dashboard: 'Suggest tasks to improve overall project health and team productivity',
        project: 'Suggest tasks specific to the current project goals and milestones',
        'task-list': 'Suggest tasks to optimize the current task workflow and organization',
        calendar: 'Suggest time-sensitive tasks and scheduling optimizations',
      };

      const prompt = contextPrompts[context];
      // For demo purposes, return mock suggestions based on context
      const mockSuggestions: AITaskSuggestion[] = [
        {
          title: `${context === 'dashboard' ? 'Weekly Team Sync' : context === 'project' ? 'Project Milestone Review' : context === 'task-list' ? 'Task Priority Audit' : 'Schedule Optimization'}`,
          description: `Context-specific suggestion for ${context}`,
          priority: 'medium',
          estimatedTime: 60,
          tags: [context, 'ai-suggested'],
        },
      ];

      setSuggestions(mockSuggestions);
    } catch (error) {
      console.error('Error generating contextual suggestions:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleTaskSuggestionClick = (task: AITaskSuggestion) => {
    setSelectedTaskSuggestion(task);
    setShowCreateTaskModal(true);
    onTaskSuggestion?.(task);
  };

  const handleTaskCreated = (task: any) => {
    console.log('Task created from quick suggestion:', task);
    setShowCreateTaskModal(false);
    setSelectedTaskSuggestion(null);
  };

  const handleCloseTaskModal = () => {
    setShowCreateTaskModal(false);
    setSelectedTaskSuggestion(null);
  };

  if (suggestions.length === 0 && !isLoading) {
    return null;
  }

  return (
    <div className={`bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-lg p-3 ${className}`}>
      <div className="flex items-center space-x-2 mb-2">
        <SparklesIcon className="h-4 w-4 text-blue-600" />
        <span className="text-sm font-medium text-blue-900">AI Suggestion</span>
        <Badge variant="outline" className="text-xs">
          Smart
        </Badge>
      </div>
      
      {isLoading ? (
        <div className="text-sm text-blue-700">Generating suggestions...</div>
      ) : (
        <div className="space-y-2">
          {suggestions.slice(0, 1).map((suggestion, index) => (
            <div key={index} className="flex items-center justify-between">
              <div className="flex-1">
                <h4 className="text-sm font-medium text-blue-900">{suggestion.title}</h4>
                <p className="text-xs text-blue-700">{suggestion.description}</p>
              </div>
              <Button
                size="sm"
                onClick={() => handleTaskSuggestionClick(suggestion)}
                className="ml-2 bg-blue-600 hover:bg-blue-700"
              >
                Create
              </Button>
            </div>
          ))}
        </div>
      )}

      {/* Task Creation Modal */}
      <CreateTaskModal
        isOpen={showCreateTaskModal}
        onClose={handleCloseTaskModal}
        onTaskCreated={handleTaskCreated}
        projectId={projectData?.currentProject?.id}
        prefilledData={selectedTaskSuggestion ? {
          title: selectedTaskSuggestion.title,
          description: selectedTaskSuggestion.description,
          priority: selectedTaskSuggestion.priority,
          estimatedTime: selectedTaskSuggestion.estimatedTime,
        } : undefined}
      />
    </div>
  );
}
