'use client';

import React, { useState, useEffect, useMemo } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  ChartBarIcon,
  ClockIcon,
  UserGroupIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon,
  CalendarIcon,
  ExclamationTriangleIcon,
} from '@heroicons/react/24/outline';

interface AnalyticsData {
  tasks: any[];
  projects: any[];
  users: any[];
  timeEntries: any[];
}

interface MetricCard {
  title: string;
  value: string | number;
  change: number;
  trend: 'up' | 'down' | 'stable';
  icon: React.ComponentType<{ className?: string }>;
}

interface AdvancedAnalyticsProps {
  data: AnalyticsData;
  dateRange: { start: Date; end: Date };
  onExport?: (format: 'pdf' | 'csv' | 'excel') => void;
}

export function AdvancedAnalytics({ data, dateRange, onExport }: AdvancedAnalyticsProps) {
  const [activeTab, setActiveTab] = useState('overview');
  const [selectedMetric, setSelectedMetric] = useState<string | null>(null);

  // Calculate key metrics
  const metrics = useMemo(() => {
    const { tasks, projects, users, timeEntries } = data;
    
    // Task completion rate
    const completedTasks = tasks.filter(t => t.status?.isCompleted).length;
    const taskCompletionRate = tasks.length > 0 ? (completedTasks / tasks.length) * 100 : 0;
    
    // Average task completion time
    const completedTasksWithTime = tasks.filter(t => t.completedAt && t.createdAt);
    const avgCompletionTime = completedTasksWithTime.length > 0
      ? completedTasksWithTime.reduce((acc, task) => {
          const completionTime = new Date(task.completedAt).getTime() - new Date(task.createdAt).getTime();
          return acc + (completionTime / (1000 * 60 * 60 * 24)); // Convert to days
        }, 0) / completedTasksWithTime.length
      : 0;
    
    // Team productivity (tasks per user)
    const activeUsers = users.filter(u => tasks.some(t => t.assigneeId === u.id));
    const tasksPerUser = activeUsers.length > 0 ? tasks.length / activeUsers.length : 0;
    
    // Project health score
    const projectHealthScore = projects.length > 0
      ? projects.reduce((acc, project) => {
          const projectTasks = tasks.filter(t => t.projectId === project.id);
          const completedProjectTasks = projectTasks.filter(t => t.status?.isCompleted);
          const projectScore = projectTasks.length > 0 ? (completedProjectTasks.length / projectTasks.length) * 100 : 0;
          return acc + projectScore;
        }, 0) / projects.length
      : 0;
    
    // Time tracking metrics
    const totalTimeLogged = timeEntries.reduce((acc, entry) => acc + (entry.duration || 0), 0);
    const avgTimePerTask = tasks.length > 0 ? totalTimeLogged / tasks.length : 0;
    
    // Overdue tasks
    const overdueTasks = tasks.filter(t => 
      t.dueDate && 
      new Date(t.dueDate) < new Date() && 
      !t.status?.isCompleted
    ).length;

    return {
      taskCompletionRate: Math.round(taskCompletionRate),
      avgCompletionTime: Math.round(avgCompletionTime * 10) / 10,
      tasksPerUser: Math.round(tasksPerUser * 10) / 10,
      projectHealthScore: Math.round(projectHealthScore),
      totalTimeLogged: Math.round(totalTimeLogged / 60), // Convert to hours
      avgTimePerTask: Math.round(avgTimePerTask / 60), // Convert to hours
      overdueTasks,
      totalTasks: tasks.length,
      totalProjects: projects.length,
      activeUsers: activeUsers.length,
    };
  }, [data]);

  const metricCards: MetricCard[] = [
    {
      title: 'Task Completion Rate',
      value: `${metrics.taskCompletionRate}%`,
      change: 5.2,
      trend: 'up',
      icon: ChartBarIcon,
    },
    {
      title: 'Avg. Completion Time',
      value: `${metrics.avgCompletionTime} days`,
      change: -1.3,
      trend: 'down',
      icon: ClockIcon,
    },
    {
      title: 'Tasks per User',
      value: metrics.tasksPerUser,
      change: 2.1,
      trend: 'up',
      icon: UserGroupIcon,
    },
    {
      title: 'Project Health Score',
      value: `${metrics.projectHealthScore}%`,
      change: 3.7,
      trend: 'up',
      icon: ArrowTrendingUpIcon,
    },
    {
      title: 'Time Logged',
      value: `${metrics.totalTimeLogged}h`,
      change: 8.4,
      trend: 'up',
      icon: ClockIcon,
    },
    {
      title: 'Overdue Tasks',
      value: metrics.overdueTasks,
      change: -15.2,
      trend: 'down',
      icon: ExclamationTriangleIcon,
    },
  ];

  // Team performance analysis
  const teamPerformance = useMemo(() => {
    return data.users.map(user => {
      const userTasks = data.tasks.filter(t => t.assigneeId === user.id);
      const completedTasks = userTasks.filter(t => t.status?.isCompleted);
      const userTimeEntries = data.timeEntries.filter(t => t.userId === user.id);
      const totalTime = userTimeEntries.reduce((acc, entry) => acc + (entry.duration || 0), 0);
      
      return {
        user,
        totalTasks: userTasks.length,
        completedTasks: completedTasks.length,
        completionRate: userTasks.length > 0 ? (completedTasks.length / userTasks.length) * 100 : 0,
        totalTime: Math.round(totalTime / 60), // Convert to hours
        avgTimePerTask: userTasks.length > 0 ? Math.round((totalTime / userTasks.length) / 60) : 0,
      };
    }).sort((a, b) => b.completionRate - a.completionRate);
  }, [data]);

  // Project performance analysis
  const projectPerformance = useMemo(() => {
    return data.projects.map(project => {
      const projectTasks = data.tasks.filter(t => t.projectId === project.id);
      const completedTasks = projectTasks.filter(t => t.status?.isCompleted);
      const overdueTasks = projectTasks.filter(t => 
        t.dueDate && 
        new Date(t.dueDate) < new Date() && 
        !t.status?.isCompleted
      );
      
      return {
        project,
        totalTasks: projectTasks.length,
        completedTasks: completedTasks.length,
        overdueTasks: overdueTasks.length,
        completionRate: projectTasks.length > 0 ? (completedTasks.length / projectTasks.length) * 100 : 0,
        healthScore: projectTasks.length > 0 ? Math.max(0, 100 - (overdueTasks.length / projectTasks.length) * 100) : 100,
      };
    }).sort((a, b) => b.healthScore - a.healthScore);
  }, [data]);

  const getTrendIcon = (trend: 'up' | 'down' | 'stable') => {
    switch (trend) {
      case 'up': return ArrowTrendingUpIcon;
      case 'down': return ArrowTrendingDownIcon;
      default: return ChartBarIcon;
    }
  };

  const getTrendColor = (trend: 'up' | 'down' | 'stable', isPositive: boolean = true) => {
    if (trend === 'stable') return 'text-muted-foreground';
    const isGood = (trend === 'up' && isPositive) || (trend === 'down' && !isPositive);
    return isGood ? 'text-green-500' : 'text-red-500';
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-semibold">Advanced Analytics</h2>
          <p className="text-muted-foreground">
            Comprehensive insights and performance metrics
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm" onClick={() => onExport?.('pdf')}>
            Export PDF
          </Button>
          <Button variant="outline" size="sm" onClick={() => onExport?.('csv')}>
            Export CSV
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {metricCards.map((metric, index) => {
          const Icon = metric.icon;
          const TrendIcon = getTrendIcon(metric.trend);
          const isPositiveMetric = !metric.title.includes('Overdue') && !metric.title.includes('Completion Time');
          
          return (
            <Card key={index} className="cursor-pointer hover:shadow-md transition-shadow">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <p className="text-sm text-muted-foreground">{metric.title}</p>
                    <p className="text-2xl font-bold">{metric.value}</p>
                    <div className="flex items-center space-x-1">
                      <TrendIcon className={`h-3 w-3 ${getTrendColor(metric.trend, isPositiveMetric)}`} />
                      <span className={`text-xs ${getTrendColor(metric.trend, isPositiveMetric)}`}>
                        {metric.change > 0 ? '+' : ''}{metric.change}%
                      </span>
                      <span className="text-xs text-muted-foreground">vs last period</span>
                    </div>
                  </div>
                  <Icon className="h-8 w-8 text-muted-foreground" />
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Detailed Analytics */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="team">Team Performance</TabsTrigger>
          <TabsTrigger value="projects">Project Health</TabsTrigger>
          <TabsTrigger value="trends">Trends</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Task Distribution</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Completed</span>
                    <span className="text-sm font-medium">{metrics.taskCompletionRate}%</span>
                  </div>
                  <div className="w-full bg-secondary rounded-full h-2">
                    <div 
                      className="bg-green-500 h-2 rounded-full" 
                      style={{ width: `${metrics.taskCompletionRate}%` }}
                    />
                  </div>
                  
                  <div className="grid grid-cols-3 gap-4 mt-4 text-center">
                    <div>
                      <p className="text-2xl font-bold text-green-500">{metrics.totalTasks - metrics.overdueTasks}</p>
                      <p className="text-xs text-muted-foreground">On Track</p>
                    </div>
                    <div>
                      <p className="text-2xl font-bold text-yellow-500">{Math.round(metrics.totalTasks * 0.2)}</p>
                      <p className="text-xs text-muted-foreground">In Progress</p>
                    </div>
                    <div>
                      <p className="text-2xl font-bold text-red-500">{metrics.overdueTasks}</p>
                      <p className="text-xs text-muted-foreground">Overdue</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Time Tracking Summary</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Total Time Logged</span>
                    <span className="text-lg font-bold">{metrics.totalTimeLogged}h</span>
                  </div>
                  
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Average per Task</span>
                    <span className="text-lg font-bold">{metrics.avgTimePerTask}h</span>
                  </div>
                  
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Active Users</span>
                    <span className="text-lg font-bold">{metrics.activeUsers}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="team" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Team Performance Rankings</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {teamPerformance.map((member, index) => (
                  <div key={member.user.id} className="flex items-center justify-between p-3 rounded-lg border">
                    <div className="flex items-center space-x-3">
                      <div className="flex items-center justify-center w-8 h-8 rounded-full bg-primary/10 text-primary font-bold text-sm">
                        #{index + 1}
                      </div>
                      <div>
                        <p className="font-medium">{member.user.name}</p>
                        <p className="text-xs text-muted-foreground">
                          {member.completedTasks}/{member.totalTasks} tasks completed
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-bold text-lg">{Math.round(member.completionRate)}%</p>
                      <p className="text-xs text-muted-foreground">{member.totalTime}h logged</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="projects" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Project Health Dashboard</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {projectPerformance.map((proj, index) => (
                  <div key={proj.project.id} className="p-4 rounded-lg border">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium">{proj.project.name}</h4>
                      <Badge 
                        variant={proj.healthScore >= 80 ? 'default' : proj.healthScore >= 60 ? 'secondary' : 'destructive'}
                      >
                        {Math.round(proj.healthScore)}% Health
                      </Badge>
                    </div>
                    
                    <div className="grid grid-cols-3 gap-4 text-sm">
                      <div>
                        <p className="text-muted-foreground">Total Tasks</p>
                        <p className="font-bold">{proj.totalTasks}</p>
                      </div>
                      <div>
                        <p className="text-muted-foreground">Completed</p>
                        <p className="font-bold text-green-500">{proj.completedTasks}</p>
                      </div>
                      <div>
                        <p className="text-muted-foreground">Overdue</p>
                        <p className="font-bold text-red-500">{proj.overdueTasks}</p>
                      </div>
                    </div>
                    
                    <div className="mt-3">
                      <div className="w-full bg-secondary rounded-full h-2">
                        <div 
                          className="bg-primary h-2 rounded-full" 
                          style={{ width: `${proj.completionRate}%` }}
                        />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="trends" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Performance Trends</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-muted-foreground">
                <ChartBarIcon className="h-12 w-12 mx-auto mb-4" />
                <p>Advanced trend analysis coming soon</p>
                <p className="text-sm">This will include time-series charts and predictive analytics</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
