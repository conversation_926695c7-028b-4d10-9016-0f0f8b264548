'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import {
  SparklesIcon,
  LightBulbIcon,
  ExclamationTriangleIcon,
  TrophyIcon,
  ArrowPathIcon,
  CheckCircleIcon,
  ClockIcon,
  UserGroupIcon,
  ChartBarIcon,
  BoltIcon,
} from '@heroicons/react/24/outline';
import { geminiAI, AIProjectInsight, AITaskSuggestion } from '@/lib/ai/gemini-service';

interface AIProjectInsightsProps {
  data: {
    tasks: any[];
    projects: any[];
    users: any[];
    timeEntries: any[];
  };
  dateRange: { start: Date; end: Date };
}

export function AIProjectInsights({ data, dateRange }: AIProjectInsightsProps) {
  const [insights, setInsights] = useState<AIProjectInsight[]>([]);
  const [taskSuggestions, setTaskSuggestions] = useState<AITaskSuggestion[]>([]);
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('insights');

  useEffect(() => {
    generateInsights();
  }, [data]);

  const generateInsights = async () => {
    setLoading(true);
    try {
      // Generate project insights
      const projectInsights = await geminiAI.analyzeProjectRisks(data);
      setInsights(projectInsights);

      // Generate task suggestions
      const context = `
        Current Project Status:
        - Total Tasks: ${data.tasks.length}
        - Completed Tasks: ${data.tasks.filter(t => t.status?.isCompleted).length}
        - Active Projects: ${data.projects.length}
        - Team Members: ${data.users.length}
        - Time Period: ${dateRange.start.toDateString()} to ${dateRange.end.toDateString()}
      `;
      const suggestions = await geminiAI.generateTaskSuggestions(context);
      setTaskSuggestions(suggestions);
    } catch (error) {
      console.error('Error generating AI insights:', error);
      // Provide fallback insights
      setInsights([
        {
          type: 'recommendation',
          title: 'Regular Progress Reviews',
          description: 'Schedule regular progress reviews to maintain project momentum and identify issues early',
          confidence: 0.8,
          actionItems: [
            'Schedule weekly team check-ins',
            'Create progress tracking dashboard',
            'Set up automated progress reports',
          ],
          impact: 'medium',
        },
        {
          type: 'opportunity',
          title: 'Process Optimization',
          description: 'Analyze current workflows to identify automation and efficiency opportunities',
          confidence: 0.7,
          actionItems: [
            'Review repetitive tasks for automation',
            'Optimize task assignment process',
            'Implement time tracking best practices',
          ],
          impact: 'high',
        },
      ]);
    } finally {
      setLoading(false);
    }
  };

  const getInsightIcon = (type: string) => {
    switch (type) {
      case 'risk': return ExclamationTriangleIcon;
      case 'opportunity': return LightBulbIcon;
      case 'recommendation': return CheckCircleIcon;
      case 'milestone': return TrophyIcon;
      default: return SparklesIcon;
    }
  };

  const getInsightColor = (type: string) => {
    switch (type) {
      case 'risk': return 'border-red-200 bg-red-50';
      case 'opportunity': return 'border-green-200 bg-green-50';
      case 'recommendation': return 'border-blue-200 bg-blue-50';
      case 'milestone': return 'border-purple-200 bg-purple-50';
      default: return 'border-gray-200 bg-gray-50';
    }
  };

  const getInsightTextColor = (type: string) => {
    switch (type) {
      case 'risk': return 'text-red-800';
      case 'opportunity': return 'text-green-800';
      case 'recommendation': return 'text-blue-800';
      case 'milestone': return 'text-purple-800';
      default: return 'text-gray-800';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'bg-red-100 text-red-800 border-red-200';
      case 'high': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'medium': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'low': return 'bg-green-100 text-green-800 border-green-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return 'text-green-600';
    if (confidence >= 0.6) return 'text-yellow-600';
    return 'text-red-600';
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-semibold flex items-center space-x-2">
            <SparklesIcon className="h-6 w-6 text-primary" />
            <span>AI-Powered Project Insights</span>
          </h2>
          <p className="text-muted-foreground">
            Get intelligent recommendations and insights powered by Gemini AI
          </p>
        </div>
        <Button onClick={generateInsights} disabled={loading}>
          {loading ? (
            <>
              <ArrowPathIcon className="h-4 w-4 mr-2 animate-spin" />
              Analyzing...
            </>
          ) : (
            <>
              <BoltIcon className="h-4 w-4 mr-2" />
              Refresh Insights
            </>
          )}
        </Button>
      </div>

      {/* AI Insights Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="insights">
            <LightBulbIcon className="h-4 w-4 mr-2" />
            Project Insights
          </TabsTrigger>
          <TabsTrigger value="suggestions">
            <CheckCircleIcon className="h-4 w-4 mr-2" />
            Task Suggestions
          </TabsTrigger>
          <TabsTrigger value="analytics">
            <ChartBarIcon className="h-4 w-4 mr-2" />
            AI Analytics
          </TabsTrigger>
        </TabsList>

        {/* Project Insights Tab */}
        <TabsContent value="insights" className="space-y-4">
          {loading ? (
            <Card>
              <CardContent className="p-8">
                <div className="text-center">
                  <ArrowPathIcon className="h-8 w-8 animate-spin mx-auto mb-4 text-primary" />
                  <p className="text-muted-foreground">AI is analyzing your project data...</p>
                </div>
              </CardContent>
            </Card>
          ) : insights.length === 0 ? (
            <Card>
              <CardContent className="p-8">
                <div className="text-center">
                  <SparklesIcon className="h-8 w-8 mx-auto mb-4 text-muted-foreground" />
                  <p className="text-muted-foreground">No insights available. Click "Refresh Insights" to generate new analysis.</p>
                </div>
              </CardContent>
            </Card>
          ) : (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
              {insights.map((insight, index) => {
                const IconComponent = getInsightIcon(insight.type);
                return (
                  <Card key={index} className={`border-2 ${getInsightColor(insight.type)}`}>
                    <CardHeader className="pb-3">
                      <div className="flex items-start justify-between">
                        <div className="flex items-center space-x-2">
                          <IconComponent className={`h-5 w-5 ${getInsightTextColor(insight.type)}`} />
                          <CardTitle className={`text-lg ${getInsightTextColor(insight.type)}`}>
                            {insight.title}
                          </CardTitle>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Badge variant="outline" className={`text-xs ${getInsightTextColor(insight.type)}`}>
                            {insight.type}
                          </Badge>
                          <Badge variant="outline" className={`text-xs ${getInsightTextColor(insight.impact)}`}>
                            {insight.impact} impact
                          </Badge>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <p className={`text-sm mb-4 ${getInsightTextColor(insight.type)}`}>
                        {insight.description}
                      </p>
                      
                      <div className="mb-4">
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-xs font-medium text-muted-foreground">AI Confidence</span>
                          <span className={`text-xs font-bold ${getConfidenceColor(insight.confidence)}`}>
                            {Math.round(insight.confidence * 100)}%
                          </span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div 
                            className={`h-2 rounded-full ${
                              insight.confidence >= 0.8 ? 'bg-green-500' : 
                              insight.confidence >= 0.6 ? 'bg-yellow-500' : 'bg-red-500'
                            }`}
                            style={{ width: `${insight.confidence * 100}%` }}
                          ></div>
                        </div>
                      </div>

                      {insight.actionItems && insight.actionItems.length > 0 && (
                        <div>
                          <h4 className={`font-medium text-sm mb-2 ${getInsightTextColor(insight.type)}`}>
                            Recommended Actions:
                          </h4>
                          <ul className="space-y-1">
                            {insight.actionItems.map((item, itemIndex) => (
                              <li key={itemIndex} className={`text-xs flex items-start ${getInsightTextColor(insight.type)}`}>
                                <span className="w-1.5 h-1.5 bg-current rounded-full mr-2 mt-1.5 flex-shrink-0"></span>
                                {item}
                              </li>
                            ))}
                          </ul>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          )}
        </TabsContent>

        {/* Task Suggestions Tab */}
        <TabsContent value="suggestions" className="space-y-4">
          {loading ? (
            <Card>
              <CardContent className="p-8">
                <div className="text-center">
                  <ArrowPathIcon className="h-8 w-8 animate-spin mx-auto mb-4 text-primary" />
                  <p className="text-muted-foreground">AI is generating task suggestions...</p>
                </div>
              </CardContent>
            </Card>
          ) : taskSuggestions.length === 0 ? (
            <Card>
              <CardContent className="p-8">
                <div className="text-center">
                  <CheckCircleIcon className="h-8 w-8 mx-auto mb-4 text-muted-foreground" />
                  <p className="text-muted-foreground">No task suggestions available. Click "Refresh Insights" to generate new suggestions.</p>
                </div>
              </CardContent>
            </Card>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {taskSuggestions.map((suggestion, index) => (
                <Card key={index} className="hover:shadow-md transition-shadow">
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <CardTitle className="text-base">{suggestion.title}</CardTitle>
                      <Badge className={getPriorityColor(suggestion.priority)}>
                        {suggestion.priority}
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm text-muted-foreground mb-4">
                      {suggestion.description}
                    </p>
                    
                    <div className="flex items-center justify-between text-xs text-muted-foreground mb-3">
                      <div className="flex items-center">
                        <ClockIcon className="h-3 w-3 mr-1" />
                        {suggestion.estimatedTime}min
                      </div>
                      <div className="flex items-center">
                        <UserGroupIcon className="h-3 w-3 mr-1" />
                        {suggestion.assigneeSuggestion || 'Unassigned'}
                      </div>
                    </div>

                    {suggestion.tags && suggestion.tags.length > 0 && (
                      <div className="flex flex-wrap gap-1 mb-3">
                        {suggestion.tags.map((tag, tagIndex) => (
                          <Badge key={tagIndex} variant="outline" className="text-xs">
                            {tag}
                          </Badge>
                        ))}
                      </div>
                    )}

                    <Button size="sm" className="w-full">
                      Create Task
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>

        {/* AI Analytics Tab */}
        <TabsContent value="analytics" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-base flex items-center">
                  <ChartBarIcon className="h-4 w-4 mr-2" />
                  AI Analysis Summary
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Insights Generated</span>
                    <span className="font-medium">{insights.length}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Task Suggestions</span>
                    <span className="font-medium">{taskSuggestions.length}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">High Confidence</span>
                    <span className="font-medium">
                      {insights.filter(i => i.confidence >= 0.8).length}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Critical Risks</span>
                    <span className="font-medium text-red-600">
                      {insights.filter(i => i.type === 'risk' && i.impact === 'high').length}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-base flex items-center">
                  <TrophyIcon className="h-4 w-4 mr-2" />
                  Opportunities
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {insights.filter(i => i.type === 'opportunity').length === 0 ? (
                    <p className="text-sm text-muted-foreground">No opportunities identified</p>
                  ) : (
                    insights.filter(i => i.type === 'opportunity').map((opportunity, index) => (
                      <div key={index} className="text-sm">
                        <p className="font-medium text-green-700">{opportunity.title}</p>
                        <p className="text-xs text-muted-foreground">{opportunity.impact} impact</p>
                      </div>
                    ))
                  )}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-base flex items-center">
                  <ExclamationTriangleIcon className="h-4 w-4 mr-2" />
                  Risk Alerts
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {insights.filter(i => i.type === 'risk').length === 0 ? (
                    <p className="text-sm text-green-600">No risks detected</p>
                  ) : (
                    insights.filter(i => i.type === 'risk').map((risk, index) => (
                      <div key={index} className="text-sm">
                        <p className="font-medium text-red-700">{risk.title}</p>
                        <p className="text-xs text-muted-foreground">{risk.impact} impact</p>
                      </div>
                    ))
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
