'use client';

import React, { useState, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  DocumentArrowDownIcon,
  PrinterIcon,
  ShareIcon,
  CalendarIcon,
  ChartBarIcon,
  ClockIcon,
  UserGroupIcon,
  TrophyIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
} from '@heroicons/react/24/outline';

interface PerformanceReportsProps {
  data: {
    tasks: any[];
    projects: any[];
    users: any[];
    timeEntries: any[];
  };
  dateRange: { start: Date; end: Date };
}

export function PerformanceReports({ data, dateRange }: PerformanceReportsProps) {
  const [reportType, setReportType] = useState('executive');
  const [exportFormat, setExportFormat] = useState('pdf');

  // Calculate comprehensive metrics
  const metrics = useMemo(() => {
    const { tasks, projects, users, timeEntries } = data;
    
    // Task metrics
    const completedTasks = tasks.filter(t => t.status?.isCompleted);
    const overdueTasks = tasks.filter(t => 
      t.dueDate && new Date(t.dueDate) < new Date() && !t.status?.isCompleted
    );
    const inProgressTasks = tasks.filter(t => !t.status?.isCompleted && !overdueTasks.includes(t));
    
    // Time metrics
    const totalTimeLogged = timeEntries.reduce((acc, entry) => acc + (entry.duration || 0), 0);
    const avgTimePerTask = tasks.length > 0 ? totalTimeLogged / tasks.length : 0;
    
    // Team metrics
    const activeUsers = users.filter(u => tasks.some(t => t.assigneeId === u.id));
    const tasksPerUser = activeUsers.length > 0 ? tasks.length / activeUsers.length : 0;
    
    // Project metrics
    const projectHealthScores = projects.map(project => {
      const projectTasks = tasks.filter(t => t.projectId === project.id);
      const completedProjectTasks = projectTasks.filter(t => t.status?.isCompleted);
      return projectTasks.length > 0 ? (completedProjectTasks.length / projectTasks.length) * 100 : 0;
    });
    const avgProjectHealth = projectHealthScores.length > 0 
      ? projectHealthScores.reduce((a, b) => a + b, 0) / projectHealthScores.length 
      : 0;
    
    // Performance indicators
    const completionRate = tasks.length > 0 ? (completedTasks.length / tasks.length) * 100 : 0;
    const onTimeRate = tasks.length > 0 ? ((tasks.length - overdueTasks.length) / tasks.length) * 100 : 0;
    const productivity = completedTasks.length / (dateRange.end.getTime() - dateRange.start.getTime()) * (1000 * 60 * 60 * 24);
    
    return {
      totalTasks: tasks.length,
      completedTasks: completedTasks.length,
      inProgressTasks: inProgressTasks.length,
      overdueTasks: overdueTasks.length,
      completionRate: Math.round(completionRate),
      onTimeRate: Math.round(onTimeRate),
      totalTimeLogged: Math.round(totalTimeLogged / 60), // Convert to hours
      avgTimePerTask: Math.round(avgTimePerTask / 60), // Convert to hours
      activeUsers: activeUsers.length,
      tasksPerUser: Math.round(tasksPerUser * 10) / 10,
      avgProjectHealth: Math.round(avgProjectHealth),
      productivity: Math.round(productivity * 100) / 100,
      totalProjects: projects.length,
    };
  }, [data, dateRange]);

  // Top performers
  const topPerformers = useMemo(() => {
    return data.users.map(user => {
      const userTasks = data.tasks.filter(t => t.assigneeId === user.id);
      const completedTasks = userTasks.filter(t => t.status?.isCompleted);
      const userTimeEntries = data.timeEntries.filter(t => t.userId === user.id);
      const totalTime = userTimeEntries.reduce((acc, entry) => acc + (entry.duration || 0), 0);
      
      return {
        user,
        completedTasks: completedTasks.length,
        totalTasks: userTasks.length,
        completionRate: userTasks.length > 0 ? (completedTasks.length / userTasks.length) * 100 : 0,
        timeLogged: Math.round(totalTime / 60),
        efficiency: userTasks.length > 0 ? Math.round((completedTasks.length / userTasks.length) * 100) : 0,
      };
    }).sort((a, b) => b.completionRate - a.completionRate);
  }, [data]);

  // Project performance
  const projectPerformance = useMemo(() => {
    return data.projects.map(project => {
      const projectTasks = data.tasks.filter(t => t.projectId === project.id);
      const completedTasks = projectTasks.filter(t => t.status?.isCompleted);
      const overdueTasks = projectTasks.filter(t => 
        t.dueDate && new Date(t.dueDate) < new Date() && !t.status?.isCompleted
      );
      
      const healthScore = projectTasks.length > 0 
        ? Math.max(0, 100 - (overdueTasks.length / projectTasks.length) * 100)
        : 100;
      
      return {
        project,
        totalTasks: projectTasks.length,
        completedTasks: completedTasks.length,
        overdueTasks: overdueTasks.length,
        completionRate: projectTasks.length > 0 ? (completedTasks.length / projectTasks.length) * 100 : 0,
        healthScore: Math.round(healthScore),
        status: healthScore >= 80 ? 'excellent' : healthScore >= 60 ? 'good' : healthScore >= 40 ? 'warning' : 'critical',
      };
    }).sort((a, b) => b.healthScore - a.healthScore);
  }, [data]);

  // Risk analysis
  const riskAnalysis = useMemo(() => {
    const risks = [];
    
    if (metrics.overdueTasks > 0) {
      risks.push({
        type: 'high',
        title: 'Overdue Tasks',
        description: `${metrics.overdueTasks} tasks are past their due date`,
        impact: 'High',
        recommendation: 'Review and reschedule overdue tasks immediately',
      });
    }
    
    if (metrics.completionRate < 70) {
      risks.push({
        type: 'medium',
        title: 'Low Completion Rate',
        description: `Task completion rate is ${metrics.completionRate}%`,
        impact: 'Medium',
        recommendation: 'Analyze task complexity and team capacity',
      });
    }
    
    if (metrics.avgProjectHealth < 60) {
      risks.push({
        type: 'medium',
        title: 'Project Health Concerns',
        description: `Average project health score is ${metrics.avgProjectHealth}%`,
        impact: 'Medium',
        recommendation: 'Focus on struggling projects and resource allocation',
      });
    }
    
    return risks;
  }, [metrics]);

  const handleExport = (format: string) => {
    console.log(`Exporting ${reportType} report as ${format}`);
    // In a real implementation, this would generate and download the report
    alert(`Exporting ${reportType} report as ${format.toUpperCase()}...`);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'excellent': return 'bg-green-100 text-green-800';
      case 'good': return 'bg-blue-100 text-blue-800';
      case 'warning': return 'bg-yellow-100 text-yellow-800';
      case 'critical': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getRiskColor = (type: string) => {
    switch (type) {
      case 'high': return 'bg-red-100 text-red-800 border-red-200';
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low': return 'bg-green-100 text-green-800 border-green-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-semibold">Performance Reports</h2>
          <p className="text-muted-foreground">
            Comprehensive performance analysis and insights
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Select value={reportType} onValueChange={setReportType}>
            <SelectTrigger className="w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="executive">Executive Summary</SelectItem>
              <SelectItem value="detailed">Detailed Analysis</SelectItem>
              <SelectItem value="team">Team Performance</SelectItem>
              <SelectItem value="project">Project Health</SelectItem>
            </SelectContent>
          </Select>
          <Select value={exportFormat} onValueChange={setExportFormat}>
            <SelectTrigger className="w-24">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="pdf">PDF</SelectItem>
              <SelectItem value="excel">Excel</SelectItem>
              <SelectItem value="csv">CSV</SelectItem>
            </SelectContent>
          </Select>
          <Button onClick={() => handleExport(exportFormat)}>
            <DocumentArrowDownIcon className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Key Metrics Summary */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Completion Rate</p>
                <p className="text-2xl font-bold">{metrics.completionRate}%</p>
              </div>
              <CheckCircleIcon className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">On-Time Rate</p>
                <p className="text-2xl font-bold">{metrics.onTimeRate}%</p>
              </div>
              <CalendarIcon className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Team Productivity</p>
                <p className="text-2xl font-bold">{metrics.productivity}</p>
              </div>
              <TrophyIcon className="h-8 w-8 text-yellow-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Project Health</p>
                <p className="text-2xl font-bold">{metrics.avgProjectHealth}%</p>
              </div>
              <ChartBarIcon className="h-8 w-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Report Content */}
      <Tabs value={reportType} onValueChange={setReportType}>
        <TabsContent value="executive" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Executive Summary */}
            <Card>
              <CardHeader>
                <CardTitle>Executive Summary</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <p className="text-muted-foreground">Total Tasks</p>
                    <p className="text-lg font-bold">{metrics.totalTasks}</p>
                  </div>
                  <div>
                    <p className="text-muted-foreground">Completed</p>
                    <p className="text-lg font-bold text-green-600">{metrics.completedTasks}</p>
                  </div>
                  <div>
                    <p className="text-muted-foreground">In Progress</p>
                    <p className="text-lg font-bold text-blue-600">{metrics.inProgressTasks}</p>
                  </div>
                  <div>
                    <p className="text-muted-foreground">Overdue</p>
                    <p className="text-lg font-bold text-red-600">{metrics.overdueTasks}</p>
                  </div>
                </div>
                
                <div className="pt-4 border-t">
                  <p className="text-sm text-muted-foreground mb-2">Key Insights:</p>
                  <ul className="text-sm space-y-1">
                    <li>• Team completed {metrics.completedTasks} tasks this period</li>
                    <li>• Average {metrics.tasksPerUser} tasks per team member</li>
                    <li>• {metrics.totalTimeLogged} hours logged across all projects</li>
                    <li>• {metrics.activeUsers} active team members contributing</li>
                  </ul>
                </div>
              </CardContent>
            </Card>

            {/* Risk Analysis */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <ExclamationTriangleIcon className="h-5 w-5" />
                  <span>Risk Analysis</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                {riskAnalysis.length === 0 ? (
                  <div className="text-center py-4 text-green-600">
                    <CheckCircleIcon className="h-8 w-8 mx-auto mb-2" />
                    <p className="font-medium">No significant risks identified</p>
                    <p className="text-sm text-muted-foreground">All metrics are within acceptable ranges</p>
                  </div>
                ) : (
                  <div className="space-y-3">
                    {riskAnalysis.map((risk, index) => (
                      <div key={index} className={`p-3 rounded-lg border ${getRiskColor(risk.type)}`}>
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <h4 className="font-medium text-sm">{risk.title}</h4>
                            <p className="text-xs mt-1">{risk.description}</p>
                            <p className="text-xs mt-2 font-medium">Recommendation: {risk.recommendation}</p>
                          </div>
                          <Badge variant="outline" className="ml-2">
                            {risk.impact} Impact
                          </Badge>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
