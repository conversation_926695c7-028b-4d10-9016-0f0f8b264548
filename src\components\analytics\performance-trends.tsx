'use client';

import React, { useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
} from 'recharts';
import {
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon,
  CalendarIcon,
  ClockIcon,
  UserGroupIcon,
  ChartBarIcon,
} from '@heroicons/react/24/outline';
import { format, subDays, startOfWeek, endOfWeek, eachDayOfInterval } from 'date-fns';

interface PerformanceTrendsProps {
  data: {
    tasks: any[];
    projects: any[];
    users: any[];
    timeEntries: any[];
  };
  dateRange: { start: Date; end: Date };
}

export function PerformanceTrends({ data, dateRange }: PerformanceTrendsProps) {
  // Generate trend data for the last 30 days
  const trendData = useMemo(() => {
    const days = eachDayOfInterval({
      start: subDays(new Date(), 29),
      end: new Date(),
    });

    return days.map((day, index) => {
      // Simulate realistic trend data based on actual data patterns
      const baseTasksCompleted = Math.floor(data.tasks.length / 30);
      const variance = Math.sin(index * 0.2) * 2 + Math.random() * 3;
      const tasksCompleted = Math.max(0, Math.floor(baseTasksCompleted + variance));
      
      const baseTimeLogged = Math.floor((data.timeEntries.reduce((acc, entry) => acc + (entry.duration || 0), 0) / 60) / 30);
      const timeVariance = Math.cos(index * 0.15) * 1.5 + Math.random() * 2;
      const timeLogged = Math.max(0, Math.floor(baseTimeLogged + timeVariance));
      
      const baseTasksCreated = Math.floor(data.tasks.length / 30);
      const createdVariance = Math.sin(index * 0.3) * 1.5 + Math.random() * 2;
      const tasksCreated = Math.max(0, Math.floor(baseTasksCreated + createdVariance));

      return {
        date: format(day, 'MMM dd'),
        fullDate: day,
        tasksCompleted,
        tasksCreated,
        timeLogged,
        activeUsers: Math.min(data.users.length, Math.floor(data.users.length * (0.7 + Math.random() * 0.3))),
        productivity: tasksCompleted > 0 ? Math.round((tasksCompleted / (tasksCreated || 1)) * 100) : 0,
      };
    });
  }, [data]);

  // Weekly aggregated data
  const weeklyData = useMemo(() => {
    const weeks = [];
    for (let i = 0; i < 8; i++) {
      const weekStart = startOfWeek(subDays(new Date(), i * 7));
      const weekEnd = endOfWeek(weekStart);
      
      const weekTasks = trendData.filter(day => 
        day.fullDate >= weekStart && day.fullDate <= weekEnd
      );
      
      weeks.unshift({
        week: `Week ${8 - i}`,
        tasksCompleted: weekTasks.reduce((sum, day) => sum + day.tasksCompleted, 0),
        timeLogged: weekTasks.reduce((sum, day) => sum + day.timeLogged, 0),
        productivity: Math.round(weekTasks.reduce((sum, day) => sum + day.productivity, 0) / weekTasks.length),
      });
    }
    return weeks;
  }, [trendData]);

  // Team performance data
  const teamPerformanceData = useMemo(() => {
    return data.users.map(user => {
      const userTasks = data.tasks.filter(t => t.assigneeId === user.id);
      const completedTasks = userTasks.filter(t => t.status?.isCompleted);
      const userTimeEntries = data.timeEntries.filter(t => t.userId === user.id);
      
      return {
        name: user.name.split(' ')[0], // First name only for chart
        tasks: userTasks.length,
        completed: completedTasks.length,
        timeLogged: Math.round(userTimeEntries.reduce((acc, entry) => acc + (entry.duration || 0), 0) / 60),
        efficiency: userTasks.length > 0 ? Math.round((completedTasks.length / userTasks.length) * 100) : 0,
      };
    });
  }, [data]);

  // Priority distribution data
  const priorityData = useMemo(() => {
    const priorities = ['low', 'medium', 'high', 'urgent'];
    const colors = ['#22c55e', '#3b82f6', '#f59e0b', '#ef4444'];
    
    return priorities.map((priority, index) => ({
      name: priority.charAt(0).toUpperCase() + priority.slice(1),
      value: data.tasks.filter(t => t.priority === priority).length,
      color: colors[index],
    }));
  }, [data]);

  // Calculate trend indicators
  const calculateTrend = (data: number[]) => {
    if (data.length < 2) return { trend: 'stable', change: 0 };
    
    const recent = data.slice(-7).reduce((a, b) => a + b, 0) / 7;
    const previous = data.slice(-14, -7).reduce((a, b) => a + b, 0) / 7;
    
    const change = ((recent - previous) / previous) * 100;
    const trend = Math.abs(change) < 5 ? 'stable' : change > 0 ? 'up' : 'down';
    
    return { trend, change: Math.round(change * 10) / 10 };
  };

  const taskCompletionTrend = calculateTrend(trendData.map(d => d.tasksCompleted));
  const timeLoggedTrend = calculateTrend(trendData.map(d => d.timeLogged));
  const productivityTrend = calculateTrend(trendData.map(d => d.productivity));

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up': return ArrowTrendingUpIcon;
      case 'down': return ArrowTrendingDownIcon;
      default: return ChartBarIcon;
    }
  };

  const getTrendColor = (trend: string) => {
    switch (trend) {
      case 'up': return 'text-green-500';
      case 'down': return 'text-red-500';
      default: return 'text-muted-foreground';
    }
  };

  return (
    <div className="space-y-6">
      {/* Trend Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Task Completion Trend</p>
                <p className="text-2xl font-bold">
                  {trendData[trendData.length - 1]?.tasksCompleted || 0}
                </p>
              </div>
              <div className="flex items-center space-x-1">
                {React.createElement(getTrendIcon(taskCompletionTrend.trend), {
                  className: `h-4 w-4 ${getTrendColor(taskCompletionTrend.trend)}`
                })}
                <span className={`text-sm ${getTrendColor(taskCompletionTrend.trend)}`}>
                  {taskCompletionTrend.change > 0 ? '+' : ''}{taskCompletionTrend.change}%
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Time Logged Trend</p>
                <p className="text-2xl font-bold">
                  {trendData[trendData.length - 1]?.timeLogged || 0}h
                </p>
              </div>
              <div className="flex items-center space-x-1">
                {React.createElement(getTrendIcon(timeLoggedTrend.trend), {
                  className: `h-4 w-4 ${getTrendColor(timeLoggedTrend.trend)}`
                })}
                <span className={`text-sm ${getTrendColor(timeLoggedTrend.trend)}`}>
                  {timeLoggedTrend.change > 0 ? '+' : ''}{timeLoggedTrend.change}%
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Productivity Trend</p>
                <p className="text-2xl font-bold">
                  {trendData[trendData.length - 1]?.productivity || 0}%
                </p>
              </div>
              <div className="flex items-center space-x-1">
                {React.createElement(getTrendIcon(productivityTrend.trend), {
                  className: `h-4 w-4 ${getTrendColor(productivityTrend.trend)}`
                })}
                <span className={`text-sm ${getTrendColor(productivityTrend.trend)}`}>
                  {productivityTrend.change > 0 ? '+' : ''}{productivityTrend.change}%
                </span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Charts */}
      <Tabs defaultValue="daily" className="space-y-4">
        <TabsList>
          <TabsTrigger value="daily">Daily Trends</TabsTrigger>
          <TabsTrigger value="weekly">Weekly Summary</TabsTrigger>
          <TabsTrigger value="team">Team Performance</TabsTrigger>
          <TabsTrigger value="distribution">Task Distribution</TabsTrigger>
        </TabsList>

        <TabsContent value="daily" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Task Completion Over Time</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart data={trendData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Line 
                      type="monotone" 
                      dataKey="tasksCompleted" 
                      stroke="#3b82f6" 
                      strokeWidth={2}
                      name="Tasks Completed"
                    />
                    <Line 
                      type="monotone" 
                      dataKey="tasksCreated" 
                      stroke="#22c55e" 
                      strokeWidth={2}
                      name="Tasks Created"
                    />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Time Logged & Productivity</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <AreaChart data={trendData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis yAxisId="left" />
                    <YAxis yAxisId="right" orientation="right" />
                    <Tooltip />
                    <Legend />
                    <Area
                      yAxisId="left"
                      type="monotone"
                      dataKey="timeLogged"
                      stackId="1"
                      stroke="#f59e0b"
                      fill="#f59e0b"
                      fillOpacity={0.6}
                      name="Hours Logged"
                    />

                  </AreaChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="weekly" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Weekly Performance Summary</CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={400}>
                <BarChart data={weeklyData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="week" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Bar dataKey="tasksCompleted" fill="#3b82f6" name="Tasks Completed" />
                  <Bar dataKey="timeLogged" fill="#22c55e" name="Hours Logged" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="team" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Team Performance Comparison</CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={400}>
                <BarChart data={teamPerformanceData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Bar dataKey="completed" fill="#22c55e" name="Completed Tasks" />
                  <Bar dataKey="timeLogged" fill="#3b82f6" name="Hours Logged" />
                  <Bar dataKey="efficiency" fill="#f59e0b" name="Efficiency %" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="distribution" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Task Priority Distribution</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={priorityData}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {priorityData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Priority Breakdown</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {priorityData.map((priority) => (
                    <div key={priority.name} className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <div 
                          className="w-3 h-3 rounded-full" 
                          style={{ backgroundColor: priority.color }}
                        />
                        <span className="text-sm font-medium">{priority.name}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <span className="text-sm font-bold">{priority.value}</span>
                        <span className="text-xs text-muted-foreground">tasks</span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
