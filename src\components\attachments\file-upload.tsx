'use client';

import React, { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import {
  CloudArrowUpIcon,
  DocumentIcon,
  PhotoIcon,
  VideoCameraIcon,
  XMarkIcon,
  EyeIcon,
  ArrowDownTrayIcon,
} from '@heroicons/react/24/outline';
import { cn } from '@/lib/utils';

export interface FileAttachment {
  id: string;
  name: string;
  size: number;
  type: string;
  url: string;
  uploadedAt: Date;
  uploadedBy: string;
  thumbnail?: string;
}

interface FileUploadProps {
  onFilesUploaded: (files: FileAttachment[]) => void;
  maxFiles?: number;
  maxSize?: number; // in bytes
  acceptedTypes?: string[];
  existingFiles?: FileAttachment[];
  className?: string;
}

export function FileUpload({
  onFilesUploaded,
  maxFiles = 10,
  maxSize = 10 * 1024 * 1024, // 10MB
  acceptedTypes = ['image/*', 'application/pdf', '.doc', '.docx', '.txt', '.csv', '.xlsx'],
  existingFiles = [],
  className,
}: FileUploadProps) {
  const [uploadingFiles, setUploadingFiles] = useState<Map<string, number>>(new Map());
  const [uploadedFiles, setUploadedFiles] = useState<FileAttachment[]>(existingFiles);

  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    const newUploads = new Map(uploadingFiles);
    
    for (const file of acceptedFiles) {
      const fileId = `${file.name}-${Date.now()}`;
      newUploads.set(fileId, 0);
      
      // Simulate file upload with progress
      simulateUpload(file, fileId, newUploads);
    }
    
    setUploadingFiles(newUploads);
  }, [uploadingFiles]);

  const simulateUpload = async (file: File, fileId: string, uploadsMap: Map<string, number>) => {
    // Simulate upload progress
    for (let progress = 0; progress <= 100; progress += 10) {
      await new Promise(resolve => setTimeout(resolve, 100));
      uploadsMap.set(fileId, progress);
      setUploadingFiles(new Map(uploadsMap));
    }

    // Create file attachment object
    const attachment: FileAttachment = {
      id: fileId,
      name: file.name,
      size: file.size,
      type: file.type,
      url: URL.createObjectURL(file), // In real app, this would be the uploaded file URL
      uploadedAt: new Date(),
      uploadedBy: 'current-user', // Would be actual user ID
      thumbnail: file.type.startsWith('image/') ? URL.createObjectURL(file) : undefined,
    };

    // Add to uploaded files
    setUploadedFiles(prev => {
      const updated = [...prev, attachment];
      onFilesUploaded(updated);
      return updated;
    });

    // Remove from uploading
    uploadsMap.delete(fileId);
    setUploadingFiles(new Map(uploadsMap));
  };

  const removeFile = (fileId: string) => {
    setUploadedFiles(prev => {
      const updated = prev.filter(f => f.id !== fileId);
      onFilesUploaded(updated);
      return updated;
    });
  };

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    maxFiles: maxFiles - uploadedFiles.length,
    maxSize,
    accept: acceptedTypes.reduce((acc, type) => {
      acc[type] = [];
      return acc;
    }, {} as Record<string, string[]>),
  });

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getFileIcon = (type: string) => {
    if (type.startsWith('image/')) return PhotoIcon;
    if (type.startsWith('video/')) return VideoCameraIcon;
    return DocumentIcon;
  };

  return (
    <div className={cn('space-y-4', className)}>
      {/* Upload Area */}
      {uploadedFiles.length < maxFiles && (
        <Card>
          <CardContent className="p-6">
            <div
              {...getRootProps()}
              className={cn(
                'border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors',
                isDragActive
                  ? 'border-primary bg-primary/5'
                  : 'border-muted-foreground/25 hover:border-primary/50'
              )}
            >
              <input {...getInputProps()} />
              <CloudArrowUpIcon className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <div className="space-y-2">
                <p className="text-lg font-medium">
                  {isDragActive ? 'Drop files here' : 'Drag & drop files here'}
                </p>
                <p className="text-sm text-muted-foreground">
                  or <span className="text-primary font-medium">browse files</span>
                </p>
                <p className="text-xs text-muted-foreground">
                  Max {maxFiles} files, up to {formatFileSize(maxSize)} each
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Uploading Files */}
      {uploadingFiles.size > 0 && (
        <div className="space-y-2">
          <h4 className="text-sm font-medium">Uploading...</h4>
          {Array.from(uploadingFiles.entries()).map(([fileId, progress]) => (
            <Card key={fileId}>
              <CardContent className="p-3">
                <div className="flex items-center space-x-3">
                  <DocumentIcon className="h-8 w-8 text-muted-foreground" />
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium truncate">{fileId.split('-')[0]}</p>
                    <Progress value={progress} className="h-2 mt-1" />
                  </div>
                  <span className="text-xs text-muted-foreground">{progress}%</span>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Uploaded Files */}
      {uploadedFiles.length > 0 && (
        <div className="space-y-2">
          <h4 className="text-sm font-medium">
            Attachments ({uploadedFiles.length})
          </h4>
          <div className="grid gap-2">
            {uploadedFiles.map((file) => {
              const FileIcon = getFileIcon(file.type);
              return (
                <Card key={file.id}>
                  <CardContent className="p-3">
                    <div className="flex items-center space-x-3">
                      {file.thumbnail ? (
                        <img
                          src={file.thumbnail}
                          alt={file.name}
                          className="h-10 w-10 rounded object-cover"
                        />
                      ) : (
                        <FileIcon className="h-10 w-10 text-muted-foreground" />
                      )}
                      
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium truncate">{file.name}</p>
                        <div className="flex items-center space-x-2 mt-1">
                          <Badge variant="outline" className="text-xs">
                            {formatFileSize(file.size)}
                          </Badge>
                          <span className="text-xs text-muted-foreground">
                            {file.uploadedAt.toLocaleDateString()}
                          </span>
                        </div>
                      </div>

                      <div className="flex items-center space-x-1">
                        <Button
                          size="icon"
                          variant="ghost"
                          className="h-8 w-8"
                          onClick={() => window.open(file.url, '_blank')}
                        >
                          <EyeIcon className="h-4 w-4" />
                        </Button>
                        <Button
                          size="icon"
                          variant="ghost"
                          className="h-8 w-8"
                          onClick={() => {
                            const a = document.createElement('a');
                            a.href = file.url;
                            a.download = file.name;
                            a.click();
                          }}
                        >
                          <ArrowDownTrayIcon className="h-4 w-4" />
                        </Button>
                        <Button
                          size="icon"
                          variant="ghost"
                          className="h-8 w-8 text-destructive hover:text-destructive"
                          onClick={() => removeFile(file.id)}
                        >
                          <XMarkIcon className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
}
