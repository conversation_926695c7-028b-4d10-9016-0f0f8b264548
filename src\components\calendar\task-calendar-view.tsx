'use client';

import React, { useState, useEffect, useMemo } from 'react';
import { Calendar, momentLocalizer, Views } from 'react-big-calendar';
import moment from 'moment';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  ChevronLeftIcon,
  ChevronRightIcon,
  CalendarIcon,
  PlusIcon,
  FunnelIcon,
  ViewColumnsIcon,
} from '@heroicons/react/24/outline';
import { apiClient } from '@/lib/api/client';
import { getPriorityColor, initials } from '@/lib/utils';
import 'react-big-calendar/lib/css/react-big-calendar.css';

const localizer = momentLocalizer(moment);

interface TaskCalendarViewProps {
  projectId: string;
  onTaskClick?: (task: any) => void;
  onCreateTask?: (date?: Date) => void;
}

export function TaskCalendarView({ projectId, onTaskClick, onCreateTask }: TaskCalendarViewProps) {
  const [tasks, setTasks] = useState<any[]>([]);
  const [users, setUsers] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [currentDate, setCurrentDate] = useState(new Date());
  const [currentView, setCurrentView] = useState<'month' | 'week' | 'day' | 'agenda'>('month');
  const [showCompleted, setShowCompleted] = useState(true);
  const [selectedUser, setSelectedUser] = useState<string>('all');

  useEffect(() => {
    fetchData();
  }, [projectId]);

  const fetchData = async () => {
    try {
      setLoading(true);
      const [tasksData, usersData] = await Promise.all([
        apiClient.getTasks({ projectId }).catch(() => {
          // Fallback to mock tasks if API fails
          return [
            {
              id: '1',
              title: 'Design System Implementation',
              description: 'Create comprehensive design system for the application',
              priority: 'high',
              dueDate: new Date(Date.now() + 86400000).toISOString(), // Tomorrow
              assigneeId: '1',
              projectId,
              status: { isCompleted: false },
            },
            {
              id: '2',
              title: 'API Integration Testing',
              description: 'Test all API endpoints and error handling',
              priority: 'urgent',
              dueDate: new Date().toISOString(), // Today
              assigneeId: '2',
              projectId,
              status: { isCompleted: false },
            },
            {
              id: '3',
              title: 'User Documentation',
              description: 'Write comprehensive user documentation',
              priority: 'medium',
              dueDate: new Date(Date.now() + 172800000).toISOString(), // Day after tomorrow
              assigneeId: '1',
              projectId,
              status: { isCompleted: true },
            },
            {
              id: '4',
              title: 'Performance Optimization',
              description: 'Optimize application performance and loading times',
              priority: 'low',
              dueDate: new Date(Date.now() - 86400000).toISOString(), // Yesterday (overdue)
              assigneeId: '3',
              projectId,
              status: { isCompleted: false },
            },
          ];
        }),
        apiClient.getUsers().catch(() => {
          // Fallback to mock users if API fails
          return [
            {
              id: '1',
              name: 'Alice Johnson',
              email: '<EMAIL>',
              avatar: '',
            },
            {
              id: '2',
              name: 'Bob Smith',
              email: '<EMAIL>',
              avatar: '',
            },
            {
              id: '3',
              name: 'Carol Davis',
              email: '<EMAIL>',
              avatar: '',
            },
          ];
        }),
      ]);

      setTasks(tasksData as any[]);
      setUsers(usersData as any[]);
    } catch (error) {
      console.error('Error fetching calendar data:', error);
      // Set fallback data on complete failure
      setTasks([
        {
          id: '1',
          title: 'Sample Task',
          description: 'This is a sample task for demonstration',
          priority: 'medium',
          dueDate: new Date().toISOString(),
          assigneeId: '1',
          projectId,
          status: { isCompleted: false },
        },
      ]);
      setUsers([
        {
          id: '1',
          name: 'Demo User',
          email: '<EMAIL>',
          avatar: '',
        },
      ]);
    } finally {
      setLoading(false);
    }
  };

  const getUserById = (userId: string) => users.find(user => user.id === userId);

  // Convert tasks to calendar events
  const calendarEvents = useMemo(() => {
    return tasks
      .filter(task => {
        // Filter by completion status
        if (!showCompleted && (task.status?.isCompleted || task.completed)) {
          return false;
        }
        
        // Filter by user
        if (selectedUser !== 'all' && task.assigneeId !== selectedUser) {
          return false;
        }
        
        // Only show tasks with due dates
        return task.dueDate;
      })
      .map(task => {
        const assignee = getUserById(task.assigneeId);
        const dueDate = new Date(task.dueDate);
        
        // Create event that spans the due date
        const startDate = new Date(dueDate);
        startDate.setHours(9, 0, 0, 0); // Default to 9 AM
        
        const endDate = new Date(dueDate);
        endDate.setHours(17, 0, 0, 0); // Default to 5 PM
        
        return {
          id: task.id,
          title: task.title,
          start: startDate,
          end: endDate,
          resource: {
            task,
            assignee,
            priority: task.priority,
            isCompleted: task.status?.isCompleted || task.completed,
            isOverdue: dueDate < new Date() && !(task.status?.isCompleted || task.completed),
          },
        };
      });
  }, [tasks, users, showCompleted, selectedUser]);

  // Custom event component
  const EventComponent = ({ event }: { event: any }) => {
    const { task, assignee, priority, isCompleted, isOverdue } = event.resource;
    
    return (
      <div className={`p-1 rounded text-xs ${isCompleted ? 'opacity-60' : ''}`}>
        <div className="flex items-center space-x-1">
          <div
            className="w-2 h-2 rounded-full flex-shrink-0"
            style={{ backgroundColor: getPriorityColor(priority) }}
          />
          <span className={`truncate ${isCompleted ? 'line-through' : ''}`}>
            {event.title}
          </span>
        </div>
        {assignee && (
          <div className="flex items-center space-x-1 mt-1">
            <Avatar className="h-3 w-3">
              <AvatarImage src={assignee.avatar} alt={assignee.name} />
              <AvatarFallback className="text-[8px]">{initials(assignee.name)}</AvatarFallback>
            </Avatar>
            <span className="text-[10px] text-muted-foreground truncate">
              {assignee.name.split(' ')[0]}
            </span>
          </div>
        )}
      </div>
    );
  };

  // Custom event style getter
  const eventStyleGetter = (event: any) => {
    const { priority, isCompleted, isOverdue } = event.resource;
    
    let backgroundColor = getPriorityColor(priority);
    let borderColor = backgroundColor;
    
    if (isCompleted) {
      backgroundColor = '#6b7280';
      borderColor = '#6b7280';
    } else if (isOverdue) {
      backgroundColor = '#ef4444';
      borderColor = '#dc2626';
    }
    
    return {
      style: {
        backgroundColor,
        borderColor,
        color: 'white',
        border: 'none',
        borderRadius: '4px',
        fontSize: '12px',
        padding: '2px 4px',
      },
    };
  };

  const handleSelectEvent = (event: any) => {
    onTaskClick?.(event.resource.task);
  };

  const handleSelectSlot = ({ start }: { start: Date }) => {
    onCreateTask?.(start);
  };

  const handleNavigate = (date: Date) => {
    setCurrentDate(date);
  };

  const handleViewChange = (view: any) => {
    setCurrentView(view);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Calendar Controls */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleNavigate(moment(currentDate).subtract(1, currentView === 'month' ? 'month' : currentView === 'week' ? 'week' : 'day').toDate())}
            >
              <ChevronLeftIcon className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleNavigate(new Date())}
            >
              Today
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleNavigate(moment(currentDate).add(1, currentView === 'month' ? 'month' : currentView === 'week' ? 'week' : 'day').toDate())}
            >
              <ChevronRightIcon className="h-4 w-4" />
            </Button>
          </div>

          <h2 className="text-lg font-semibold">
            {moment(currentDate).format(currentView === 'month' ? 'MMMM YYYY' : currentView === 'week' ? 'MMM DD, YYYY' : 'MMMM DD, YYYY')}
          </h2>
        </div>

        <div className="flex items-center space-x-2">
          {/* View Toggle */}
          <div className="flex items-center space-x-1">
            {['month', 'week', 'day', 'agenda'].map((view) => (
              <Button
                key={view}
                variant={currentView === view ? 'default' : 'ghost'}
                size="sm"
                onClick={() => handleViewChange(view)}
              >
                {view.charAt(0).toUpperCase() + view.slice(1)}
              </Button>
            ))}
          </div>

          {/* Filters */}
          <select
            value={selectedUser}
            onChange={(e) => setSelectedUser(e.target.value)}
            className="px-3 py-2 border rounded-md text-sm"
          >
            <option value="all">All Assignees</option>
            {users.map(user => (
              <option key={user.id} value={user.id}>{user.name}</option>
            ))}
          </select>

          <Button
            variant={showCompleted ? 'default' : 'outline'}
            size="sm"
            onClick={() => setShowCompleted(!showCompleted)}
          >
            {showCompleted ? 'Hide' : 'Show'} Completed
          </Button>

          <Button onClick={() => onCreateTask?.()}>
            <PlusIcon className="mr-2 h-4 w-4" />
            New Task
          </Button>
        </div>
      </div>

      {/* Calendar Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold">{calendarEvents.length}</div>
            <div className="text-sm text-muted-foreground">Total Tasks</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-red-500">
              {calendarEvents.filter(e => e.resource.isOverdue).length}
            </div>
            <div className="text-sm text-muted-foreground">Overdue</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-orange-500">
              {calendarEvents.filter(e => moment(e.start).isSame(moment(), 'day')).length}
            </div>
            <div className="text-sm text-muted-foreground">Due Today</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-green-500">
              {calendarEvents.filter(e => e.resource.isCompleted).length}
            </div>
            <div className="text-sm text-muted-foreground">Completed</div>
          </CardContent>
        </Card>
      </div>

      {/* Calendar */}
      <Card>
        <CardContent className="p-6">
          <div style={{ height: '600px' }}>
            <Calendar
              localizer={localizer}
              events={calendarEvents}
              startAccessor="start"
              endAccessor="end"
              date={currentDate}
              view={currentView}
              views={['month', 'week', 'day', 'agenda']}
              onNavigate={handleNavigate}
              onView={handleViewChange}
              onSelectEvent={handleSelectEvent}
              onSelectSlot={handleSelectSlot}
              selectable
              components={{
                event: EventComponent,
              }}
              eventPropGetter={eventStyleGetter}
              popup
              showMultiDayTimes
              step={60}
              timeslots={1}
            />
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
