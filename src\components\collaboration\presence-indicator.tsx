'use client';

import React from 'react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { useCollaboration } from '@/lib/realtime/collaboration';
import { initials } from '@/lib/utils';

interface PresenceIndicatorProps {
  location: string;
  currentUserId?: string;
}

export function PresenceIndicator({ location, currentUserId }: PresenceIndicatorProps) {
  const { presenceUsers, typingUsers } = useCollaboration();

  const locationUsers = presenceUsers.filter(
    user => user.location === location && user.userId !== currentUserId
  );

  const locationTypingUsers = typingUsers.filter(
    user => user.location === location && user.userId !== currentUserId
  );

  if (locationUsers.length === 0 && locationTypingUsers.length === 0) {
    return null;
  }

  return (
    <div className="flex items-center space-x-2">
      {/* Active Users */}
      {locationUsers.length > 0 && (
        <div className="flex items-center space-x-1">
          <div className="flex -space-x-1">
            {locationUsers.slice(0, 3).map((user) => (
              <div key={user.userId} className="relative">
                <Avatar className="h-6 w-6 border-2 border-background">
                  <AvatarImage src={user.avatar} alt={user.userName} />
                  <AvatarFallback className="text-xs">
                    {initials(user.userName)}
                  </AvatarFallback>
                </Avatar>
                <div className="absolute -bottom-0.5 -right-0.5 h-2 w-2 bg-green-500 rounded-full border border-background"></div>
              </div>
            ))}
          </div>
          
          {locationUsers.length > 3 && (
            <Badge variant="secondary" className="text-xs">
              +{locationUsers.length - 3}
            </Badge>
          )}
          
          <span className="text-xs text-muted-foreground">
            {locationUsers.length === 1 
              ? `${locationUsers[0].userName} is here`
              : `${locationUsers.length} people here`
            }
          </span>
        </div>
      )}

      {/* Typing Indicators */}
      {locationTypingUsers.length > 0 && (
        <div className="flex items-center space-x-1">
          <div className="flex space-x-1">
            <div className="flex space-x-1">
              <div className="w-1 h-1 bg-primary rounded-full animate-bounce"></div>
              <div className="w-1 h-1 bg-primary rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
              <div className="w-1 h-1 bg-primary rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
            </div>
          </div>
          <span className="text-xs text-muted-foreground">
            {locationTypingUsers.length === 1
              ? `${locationTypingUsers[0].userName} is typing...`
              : `${locationTypingUsers.length} people are typing...`
            }
          </span>
        </div>
      )}
    </div>
  );
}
