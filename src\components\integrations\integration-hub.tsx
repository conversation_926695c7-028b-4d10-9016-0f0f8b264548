'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  ChatBubbleLeftRightIcon,
  CodeBracketIcon,
  CogIcon,
  CheckCircleIcon,
  XCircleIcon,
  ArrowPathIcon,
} from '@heroicons/react/24/outline';
import {
  slackIntegration,
  githubIntegration,
  jiraIntegration,
  integrationManager,
  SlackChannel,
  GitHubRepo,
  JiraProject,
} from '@/lib/integrations/third-party-integrations';

interface IntegrationStatus {
  slack: boolean;
  github: boolean;
  jira: boolean;
}

export function IntegrationHub() {
  const [activeTab, setActiveTab] = useState('overview');
  const [integrationStatus, setIntegrationStatus] = useState<IntegrationStatus>({
    slack: false,
    github: false,
    jira: false,
  });
  const [loading, setLoading] = useState<Record<string, boolean>>({});
  
  // Slack state
  const [slackChannels, setSlackChannels] = useState<SlackChannel[]>([]);
  const [slackWorkspace, setSlackWorkspace] = useState('');
  
  // GitHub state
  const [githubRepos, setGithubRepos] = useState<GitHubRepo[]>([]);
  
  // Jira state
  const [jiraProjects, setJiraProjects] = useState<JiraProject[]>([]);
  const [jiraDomain, setJiraDomain] = useState('');
  const [jiraEmail, setJiraEmail] = useState('');
  const [jiraToken, setJiraToken] = useState('');

  useEffect(() => {
    checkIntegrationStatus();
  }, []);

  const checkIntegrationStatus = () => {
    setIntegrationStatus({
      slack: slackIntegration.isAuthenticated(),
      github: githubIntegration.isAuthenticated(),
      jira: jiraIntegration.isAuthenticated(),
    });
  };

  const handleSlackConnect = async () => {
    if (!slackWorkspace) return;
    
    setLoading(prev => ({ ...prev, slack: true }));
    try {
      const success = await slackIntegration.authenticate(slackWorkspace);
      if (success) {
        const channels = await slackIntegration.getChannels();
        setSlackChannels(channels);
        checkIntegrationStatus();
      }
    } catch (error) {
      console.error('Slack connection failed:', error);
    } finally {
      setLoading(prev => ({ ...prev, slack: false }));
    }
  };

  const handleGitHubConnect = async () => {
    setLoading(prev => ({ ...prev, github: true }));
    try {
      const success = await githubIntegration.authenticate();
      if (success) {
        const repos = await githubIntegration.getRepositories();
        setGithubRepos(repos);
        checkIntegrationStatus();
      }
    } catch (error) {
      console.error('GitHub connection failed:', error);
    } finally {
      setLoading(prev => ({ ...prev, github: false }));
    }
  };

  const handleJiraConnect = async () => {
    if (!jiraDomain || !jiraEmail || !jiraToken) return;
    
    setLoading(prev => ({ ...prev, jira: true }));
    try {
      const success = await jiraIntegration.authenticate(jiraDomain, jiraEmail, jiraToken);
      if (success) {
        const projects = await jiraIntegration.getProjects();
        setJiraProjects(projects);
        checkIntegrationStatus();
      }
    } catch (error) {
      console.error('Jira connection failed:', error);
    } finally {
      setLoading(prev => ({ ...prev, jira: false }));
    }
  };

  const handleDisconnect = (platform: keyof IntegrationStatus) => {
    switch (platform) {
      case 'slack':
        slackIntegration.disconnect();
        setSlackChannels([]);
        break;
      case 'github':
        githubIntegration.disconnect();
        setGithubRepos([]);
        break;
      case 'jira':
        jiraIntegration.disconnect();
        setJiraProjects([]);
        break;
    }
    checkIntegrationStatus();
  };

  const integrations = [
    {
      id: 'slack',
      name: 'Slack',
      description: 'Send notifications and updates to Slack channels',
      icon: ChatBubbleLeftRightIcon,
      color: 'bg-purple-500',
      connected: integrationStatus.slack,
      features: ['Task notifications', 'Project updates', 'Team mentions', 'Custom webhooks'],
    },
    {
      id: 'github',
      name: 'GitHub',
      description: 'Sync tasks with GitHub issues and repositories',
      icon: CodeBracketIcon,
      color: 'bg-gray-800',
      connected: integrationStatus.github,
      features: ['Issue sync', 'Repository linking', 'Commit tracking', 'Pull request updates'],
    },
    {
      id: 'jira',
      name: 'Jira',
      description: 'Integrate with Atlassian Jira for issue tracking',
      icon: CogIcon,
      color: 'bg-blue-600',
      connected: integrationStatus.jira,
      features: ['Issue sync', 'Project mapping', 'Status updates', 'Sprint planning'],
    },
  ];

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-semibold">Integration Hub</h2>
          <p className="text-muted-foreground">
            Connect Bordio with your favorite tools and services
          </p>
        </div>
        <Button variant="outline" onClick={checkIntegrationStatus}>
          <ArrowPathIcon className="h-4 w-4 mr-2" />
          Refresh Status
        </Button>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="slack">Slack</TabsTrigger>
          <TabsTrigger value="github">GitHub</TabsTrigger>
          <TabsTrigger value="jira">Jira</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {integrations.map((integration) => {
              const Icon = integration.icon;
              return (
                <Card key={integration.id} className="relative overflow-hidden">
                  <div className={`absolute top-0 left-0 w-full h-1 ${integration.color}`} />
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className={`p-2 rounded-lg ${integration.color} text-white`}>
                          <Icon className="h-5 w-5" />
                        </div>
                        <div>
                          <CardTitle className="text-lg">{integration.name}</CardTitle>
                          <p className="text-sm text-muted-foreground">
                            {integration.description}
                          </p>
                        </div>
                      </div>
                      {integration.connected ? (
                        <CheckCircleIcon className="h-6 w-6 text-green-500" />
                      ) : (
                        <XCircleIcon className="h-6 w-6 text-muted-foreground" />
                      )}
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Status</span>
                        <Badge variant={integration.connected ? 'default' : 'secondary'}>
                          {integration.connected ? 'Connected' : 'Not Connected'}
                        </Badge>
                      </div>
                      
                      <div>
                        <p className="text-sm font-medium mb-2">Features</p>
                        <ul className="text-xs space-y-1">
                          {integration.features.map((feature, index) => (
                            <li key={index} className="flex items-center">
                              <span className="w-1 h-1 bg-muted-foreground rounded-full mr-2" />
                              {feature}
                            </li>
                          ))}
                        </ul>
                      </div>
                      
                      <Button
                        className="w-full"
                        variant={integration.connected ? 'outline' : 'default'}
                        onClick={() => setActiveTab(integration.id)}
                      >
                        {integration.connected ? 'Manage' : 'Connect'}
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </TabsContent>

        <TabsContent value="slack" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <ChatBubbleLeftRightIcon className="h-5 w-5" />
                <span>Slack Integration</span>
                {integrationStatus.slack && (
                  <Badge variant="default">Connected</Badge>
                )}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {!integrationStatus.slack ? (
                <div className="space-y-4">
                  <div>
                    <label className="text-sm font-medium">Slack Workspace URL</label>
                    <Input
                      value={slackWorkspace}
                      onChange={(e) => setSlackWorkspace(e.target.value)}
                      placeholder="your-workspace.slack.com"
                      className="mt-1"
                    />
                  </div>
                  <Button
                    onClick={handleSlackConnect}
                    disabled={loading.slack || !slackWorkspace}
                    className="w-full"
                  >
                    {loading.slack ? 'Connecting...' : 'Connect to Slack'}
                  </Button>
                </div>
              ) : (
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Connected Workspace</span>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDisconnect('slack')}
                    >
                      Disconnect
                    </Button>
                  </div>
                  
                  <div>
                    <h4 className="text-sm font-medium mb-2">Available Channels</h4>
                    <div className="space-y-2">
                      {slackChannels.map((channel) => (
                        <div key={channel.id} className="flex items-center justify-between p-2 border rounded">
                          <div>
                            <span className="font-medium">#{channel.name}</span>
                            <span className="text-xs text-muted-foreground ml-2">
                              {channel.memberCount} members
                            </span>
                          </div>
                          <Badge variant={channel.isPrivate ? 'secondary' : 'outline'}>
                            {channel.isPrivate ? 'Private' : 'Public'}
                          </Badge>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="github" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <CodeBracketIcon className="h-5 w-5" />
                <span>GitHub Integration</span>
                {integrationStatus.github && (
                  <Badge variant="default">Connected</Badge>
                )}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {!integrationStatus.github ? (
                <div className="space-y-4">
                  <p className="text-sm text-muted-foreground">
                    Connect your GitHub account to sync tasks with issues and repositories.
                  </p>
                  <Button
                    onClick={handleGitHubConnect}
                    disabled={loading.github}
                    className="w-full"
                  >
                    {loading.github ? 'Connecting...' : 'Connect to GitHub'}
                  </Button>
                </div>
              ) : (
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">GitHub Account Connected</span>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDisconnect('github')}
                    >
                      Disconnect
                    </Button>
                  </div>
                  
                  <div>
                    <h4 className="text-sm font-medium mb-2">Available Repositories</h4>
                    <div className="space-y-2">
                      {githubRepos.map((repo) => (
                        <div key={repo.id} className="p-3 border rounded">
                          <div className="flex items-center justify-between">
                            <div>
                              <span className="font-medium">{repo.name}</span>
                              <p className="text-xs text-muted-foreground">{repo.description}</p>
                            </div>
                            <Badge variant={repo.private ? 'secondary' : 'outline'}>
                              {repo.private ? 'Private' : 'Public'}
                            </Badge>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="jira" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <CogIcon className="h-5 w-5" />
                <span>Jira Integration</span>
                {integrationStatus.jira && (
                  <Badge variant="default">Connected</Badge>
                )}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {!integrationStatus.jira ? (
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium">Jira Domain</label>
                      <Input
                        value={jiraDomain}
                        onChange={(e) => setJiraDomain(e.target.value)}
                        placeholder="your-domain"
                        className="mt-1"
                      />
                      <p className="text-xs text-muted-foreground mt-1">
                        Just the domain name (without .atlassian.net)
                      </p>
                    </div>
                    <div>
                      <label className="text-sm font-medium">Email</label>
                      <Input
                        type="email"
                        value={jiraEmail}
                        onChange={(e) => setJiraEmail(e.target.value)}
                        placeholder="<EMAIL>"
                        className="mt-1"
                      />
                    </div>
                  </div>
                  <div>
                    <label className="text-sm font-medium">API Token</label>
                    <Input
                      type="password"
                      value={jiraToken}
                      onChange={(e) => setJiraToken(e.target.value)}
                      placeholder="Your Jira API token"
                      className="mt-1"
                    />
                    <p className="text-xs text-muted-foreground mt-1">
                      Generate an API token from your Jira account settings
                    </p>
                  </div>
                  <Button
                    onClick={handleJiraConnect}
                    disabled={loading.jira || !jiraDomain || !jiraEmail || !jiraToken}
                    className="w-full"
                  >
                    {loading.jira ? 'Connecting...' : 'Connect to Jira'}
                  </Button>
                </div>
              ) : (
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Jira Account Connected</span>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDisconnect('jira')}
                    >
                      Disconnect
                    </Button>
                  </div>
                  
                  <div>
                    <h4 className="text-sm font-medium mb-2">Available Projects</h4>
                    <div className="space-y-2">
                      {jiraProjects.map((project) => (
                        <div key={project.id} className="p-3 border rounded">
                          <div className="flex items-center justify-between">
                            <div>
                              <span className="font-medium">{project.name} ({project.key})</span>
                              <p className="text-xs text-muted-foreground">{project.description}</p>
                            </div>
                            <Badge variant="outline">{project.projectTypeKey}</Badge>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
