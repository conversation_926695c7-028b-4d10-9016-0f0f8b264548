'use client';

import React, { useState, useEffect } from 'react';
import {
  DndContext,
  DragEndEvent,
  DragOverEvent,
  DragOverlay,
  DragStartEvent,
  PointerSensor,
  useSensor,
  useSensors,
  closestCorners,
} from '@dnd-kit/core';
import {
  SortableContext,
  verticalListSortingStrategy,
  arrayMove,
} from '@dnd-kit/sortable';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { PlusIcon } from '@heroicons/react/24/outline';
import { KanbanColumn } from './kanban-column';
import { KanbanTask } from './kanban-task';
import { apiClient } from '@/lib/api/client';
import { getPriorityColor } from '@/lib/utils';

interface KanbanBoardProps {
  projectId: string;
  onTaskClick?: (task: any) => void;
  onCreateTask?: (statusId: string) => void;
}

export function KanbanBoard({ projectId, onTaskClick, onCreateTask }: KanbanBoardProps) {
  const [tasks, setTasks] = useState<any[]>([]);
  const [statuses, setStatuses] = useState<any[]>([]);
  const [activeTask, setActiveTask] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    })
  );

  useEffect(() => {
    fetchData();
  }, [projectId]);

  const fetchData = async () => {
    try {
      setLoading(true);
      const [tasksData, statusesData] = await Promise.all([
        apiClient.getTasks({ projectId }).catch(() => {
          // Fallback to mock tasks if API fails
          return [
            {
              id: '1',
              title: 'Design System Implementation',
              description: 'Create comprehensive design system',
              priority: 'high',
              assigneeId: '1',
              projectId,
              statusId: 'status-1',
              status: { isCompleted: false },
            },
            {
              id: '2',
              title: 'API Integration Testing',
              description: 'Test all API endpoints',
              priority: 'urgent',
              assigneeId: '2',
              projectId,
              statusId: 'status-2',
              status: { isCompleted: false },
            },
            {
              id: '3',
              title: 'User Documentation',
              description: 'Write comprehensive user documentation',
              priority: 'medium',
              assigneeId: '1',
              projectId,
              statusId: 'status-4',
              status: { isCompleted: true },
            },
          ];
        }),
        apiClient.getTaskStatuses(projectId).catch(() => {
          // Fallback to default statuses if API fails
          return [
            { id: 'status-1', name: 'To Do', color: '#6b7280', position: 0 },
            { id: 'status-2', name: 'In Progress', color: '#3b82f6', position: 1 },
            { id: 'status-3', name: 'Review', color: '#f59e0b', position: 2 },
            { id: 'status-4', name: 'Done', color: '#22c55e', position: 3 },
          ];
        }),
      ]);

      // Map tasks to have proper statusId if they don't have one
      const mappedTasks = (tasksData as any[]).map(task => ({
        ...task,
        statusId: task.statusId || (task.status?.isCompleted ? 'status-4' : 'status-1')
      }));

      setTasks(mappedTasks);
      setStatuses(statusesData);
    } catch (error) {
      console.error('Error fetching kanban data:', error);
      // Set default statuses and empty tasks on error
      setTasks([]);
      setStatuses([
        { id: 'status-1', name: 'To Do', color: '#6b7280', position: 0 },
        { id: 'status-2', name: 'In Progress', color: '#3b82f6', position: 1 },
        { id: 'status-3', name: 'Review', color: '#f59e0b', position: 2 },
        { id: 'status-4', name: 'Done', color: '#22c55e', position: 3 },
      ]);
    } finally {
      setLoading(false);
    }
  };

  const getTasksByStatus = (statusId: string) => {
    return tasks
      .filter(task => task.statusId === statusId)
      .sort((a, b) => (a.position || 0) - (b.position || 0));
  };

  const handleDragStart = (event: DragStartEvent) => {
    const { active } = event;
    const task = tasks.find(t => t.id === active.id);
    setActiveTask(task);
  };

  const handleDragOver = (event: DragOverEvent) => {
    const { active, over } = event;
    
    if (!over) return;

    const activeTask = tasks.find(t => t.id === active.id);
    if (!activeTask) return;

    const overId = over.id;
    
    // Check if we're dropping over a status column
    const overStatus = statuses.find(s => s.id === overId);
    if (overStatus && activeTask.statusId !== overStatus.id) {
      // Move task to different status
      setTasks(prevTasks => 
        prevTasks.map(task => 
          task.id === activeTask.id 
            ? { ...task, statusId: overStatus.id }
            : task
        )
      );
    }
  };

  const handleDragEnd = async (event: DragEndEvent) => {
    const { active, over } = event;
    setActiveTask(null);

    if (!over) return;

    const activeTask = tasks.find(t => t.id === active.id);
    if (!activeTask) return;

    const overId = over.id;
    
    // Check if we're reordering within the same status
    const overTask = tasks.find(t => t.id === overId);
    const overStatus = statuses.find(s => s.id === overId);
    
    if (overTask && activeTask.statusId === overTask.statusId) {
      // Reorder within same status
      const statusTasks = getTasksByStatus(activeTask.statusId);
      const oldIndex = statusTasks.findIndex(t => t.id === activeTask.id);
      const newIndex = statusTasks.findIndex(t => t.id === overTask.id);
      
      if (oldIndex !== newIndex) {
        const reorderedTasks = arrayMove(statusTasks, oldIndex, newIndex);
        
        // Update positions
        const updatedTasks = tasks.map(task => {
          const reorderedTask = reorderedTasks.find(rt => rt.id === task.id);
          if (reorderedTask) {
            const newPosition = reorderedTasks.indexOf(reorderedTask);
            return { ...task, position: newPosition };
          }
          return task;
        });
        
        setTasks(updatedTasks);
        
        // Update task position in database
        try {
          await apiClient.updateTask(activeTask.id, {
            position: reorderedTasks.findIndex(t => t.id === activeTask.id),
          });
        } catch (error) {
          console.error('Error updating task position:', error);
          // Revert on error
          fetchData();
        }
      }
    } else if (overStatus) {
      // Move to different status
      try {
        await apiClient.updateTask(activeTask.id, {
          statusId: overStatus.id,
          position: getTasksByStatus(overStatus.id).length,
        });
        
        // Refresh data to get updated state
        fetchData();
      } catch (error) {
        console.error('Error updating task status:', error);
        // Revert on error
        fetchData();
      }
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <DndContext
      sensors={sensors}
      collisionDetection={closestCorners}
      onDragStart={handleDragStart}
      onDragOver={handleDragOver}
      onDragEnd={handleDragEnd}
    >
      <div className="flex space-x-6 h-full overflow-x-auto pb-6">
        {statuses.map((status) => {
          const statusTasks = getTasksByStatus(status.id);
          
          return (
            <div key={status.id} className="flex-shrink-0 w-80">
              <KanbanColumn
                status={status}
                tasks={statusTasks}
                onTaskClick={onTaskClick}
                onCreateTask={() => onCreateTask?.(status.id)}
              />
            </div>
          );
        })}
      </div>

      <DragOverlay>
        {activeTask ? (
          <div className="rotate-3 opacity-90">
            <KanbanTask task={activeTask} isDragging />
          </div>
        ) : null}
      </DragOverlay>
    </DndContext>
  );
}
