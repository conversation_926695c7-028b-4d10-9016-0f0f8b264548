'use client';

import React from 'react';
import { useDroppable } from '@dnd-kit/core';
import { SortableContext, verticalListSortingStrategy } from '@dnd-kit/sortable';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { PlusIcon } from '@heroicons/react/24/outline';
import { KanbanTask } from './kanban-task';
import { cn } from '@/lib/utils';

interface KanbanColumnProps {
  status: any;
  tasks: any[];
  onTaskClick?: (task: any) => void;
  onCreateTask?: () => void;
}

export function KanbanColumn({ status, tasks, onTaskClick, onCreateTask }: KanbanColumnProps) {
  const { setNodeRef, isOver } = useDroppable({
    id: status.id,
  });

  return (
    <Card className={cn(
      "h-full flex flex-col",
      isOver && "ring-2 ring-primary ring-opacity-50"
    )}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div 
              className="h-3 w-3 rounded-full" 
              style={{ backgroundColor: status.color }}
            />
            <CardTitle className="text-sm font-medium">{status.name}</CardTitle>
            <Badge variant="secondary" className="text-xs">
              {tasks.length}
            </Badge>
          </div>
          <Button 
            size="icon" 
            variant="ghost" 
            className="h-6 w-6"
            onClick={onCreateTask}
          >
            <PlusIcon className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>

      <CardContent 
        ref={setNodeRef}
        className="flex-1 space-y-3 overflow-y-auto"
      >
        <SortableContext 
          items={tasks.map(task => task.id)} 
          strategy={verticalListSortingStrategy}
        >
          {tasks.map((task) => (
            <KanbanTask
              key={task.id}
              task={task}
              onClick={() => onTaskClick?.(task)}
            />
          ))}
        </SortableContext>
        
        {tasks.length === 0 && (
          <div className="text-center py-8 text-muted-foreground">
            <p className="text-sm">No tasks</p>
            <Button 
              variant="ghost" 
              size="sm" 
              className="mt-2"
              onClick={onCreateTask}
            >
              <PlusIcon className="mr-2 h-4 w-4" />
              Add task
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
