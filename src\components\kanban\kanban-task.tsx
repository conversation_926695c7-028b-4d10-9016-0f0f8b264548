'use client';

import React from 'react';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
  CalendarIcon, 
  ClockIcon, 
  ChatBubbleLeftIcon,
  PaperClipIcon 
} from '@heroicons/react/24/outline';
import { formatRelativeDate, initials, getPriorityColor } from '@/lib/utils';
import { cn } from '@/lib/utils';

interface KanbanTaskProps {
  task: any;
  onClick?: () => void;
  isDragging?: boolean;
}

export function KanbanTask({ task, onClick, isDragging = false }: KanbanTaskProps) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging: isSortableDragging,
  } = useSortable({
    id: task.id,
  });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  const isBeingDragged = isDragging || isSortableDragging;

  return (
    <Card
      ref={setNodeRef}
      style={style}
      {...attributes}
      {...listeners}
      className={cn(
        "cursor-pointer hover:shadow-md transition-all duration-200",
        isBeingDragged && "opacity-50 rotate-3 shadow-lg"
      )}
      onClick={onClick}
    >
      <CardContent className="p-4">
        <div className="space-y-3">
          {/* Task Title */}
          <div>
            <h4 className="font-medium text-sm line-clamp-2">{task.title}</h4>
            {task.description && (
              <p className="text-xs text-muted-foreground mt-1 line-clamp-2">
                {task.description}
              </p>
            )}
          </div>

          {/* Tags */}
          {task.tags && task.tags.length > 0 && (
            <div className="flex flex-wrap gap-1">
              {task.tags.slice(0, 2).map((tagId: string) => (
                <Badge key={tagId} variant="outline" className="text-xs">
                  Tag {tagId.split('-')[1]}
                </Badge>
              ))}
              {task.tags.length > 2 && (
                <Badge variant="outline" className="text-xs">
                  +{task.tags.length - 2}
                </Badge>
              )}
            </div>
          )}

          {/* Priority Badge */}
          <div className="flex items-center justify-between">
            <Badge 
              variant="outline"
              className="text-xs"
              style={{ 
                borderColor: getPriorityColor(task.priority),
                color: getPriorityColor(task.priority)
              }}
            >
              {task.priority}
            </Badge>

            {/* Assignee Avatar */}
            {task.assignee && (
              <Avatar className="h-6 w-6">
                <AvatarImage src={task.assignee.avatar} alt={task.assignee.name} />
                <AvatarFallback className="text-xs">
                  {initials(task.assignee.name)}
                </AvatarFallback>
              </Avatar>
            )}
          </div>

          {/* Task Metadata */}
          <div className="flex items-center justify-between text-xs text-muted-foreground">
            <div className="flex items-center space-x-3">
              {/* Due Date */}
              {task.dueDate && (
                <div className="flex items-center space-x-1">
                  <CalendarIcon className="h-3 w-3" />
                  <span className={cn(
                    new Date(task.dueDate) < new Date() && !task.status?.isCompleted
                      ? "text-destructive"
                      : ""
                  )}>
                    {formatRelativeDate(task.dueDate)}
                  </span>
                </div>
              )}

              {/* Time Estimate */}
              {task.estimatedTime && (
                <div className="flex items-center space-x-1">
                  <ClockIcon className="h-3 w-3" />
                  <span>{Math.round(task.estimatedTime / 60)}h</span>
                </div>
              )}
            </div>

            <div className="flex items-center space-x-2">
              {/* Comments Count */}
              {task.comments && task.comments.length > 0 && (
                <div className="flex items-center space-x-1">
                  <ChatBubbleLeftIcon className="h-3 w-3" />
                  <span>{task.comments.length}</span>
                </div>
              )}

              {/* Attachments Count */}
              {task.attachments && task.attachments.length > 0 && (
                <div className="flex items-center space-x-1">
                  <PaperClipIcon className="h-3 w-3" />
                  <span>{task.attachments.length}</span>
                </div>
              )}
            </div>
          </div>

          {/* Progress Bar for Subtasks */}
          {task.subtasks && task.subtasks.length > 0 && (
            <div className="space-y-1">
              <div className="flex justify-between text-xs text-muted-foreground">
                <span>Subtasks</span>
                <span>
                  {task.subtasks.filter((st: any) => st.completed).length} / {task.subtasks.length}
                </span>
              </div>
              <div className="w-full bg-secondary rounded-full h-1">
                <div 
                  className="bg-primary h-1 rounded-full transition-all" 
                  style={{ 
                    width: `${(task.subtasks.filter((st: any) => st.completed).length / task.subtasks.length) * 100}%` 
                  }}
                />
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
