'use client';

import React from 'react';
import { Sidebar } from './sidebar';
import { ClientOnly } from '../ui/client-only';
import { FloatingAIAssistant } from '@/components/ai/floating-ai-assistant';

interface MainLayoutProps {
  children: React.ReactNode;
  projectData?: {
    projects: any[];
    tasks: any[];
    users: any[];
    currentProject?: any;
  };
}

export function MainLayout({ children, projectData }: MainLayoutProps) {
  return (
    <div className="flex h-screen bg-background" suppressHydrationWarning>
      <ClientOnly>
        <Sidebar />
      </ClientOnly>
      <main className="flex-1 overflow-hidden" suppressHydrationWarning>
        {children}
      </main>
      <ClientOnly>
        <FloatingAIAssistant
          projectData={projectData}
          mode="both"
          position="bottom-right"
          onTaskSuggestion={(task) => {
            console.log('AI suggested task:', task);
            // In a real implementation, this would integrate with the task creation system
          }}
          onInsightAction={(insight) => {
            console.log('AI insight action:', insight);
            // In a real implementation, this would trigger appropriate actions
          }}
        />
      </ClientOnly>
    </div>
  );
}
