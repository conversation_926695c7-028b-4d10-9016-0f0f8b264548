'use client';

import React from 'react';
import { Sidebar } from './sidebar';
import { ClientOnly } from '../ui/client-only';

interface MainLayoutProps {
  children: React.ReactNode;
}

export function MainLayout({ children }: MainLayoutProps) {
  return (
    <div className="flex h-screen bg-background" suppressHydrationWarning>
      <ClientOnly>
        <Sidebar />
      </ClientOnly>
      <main className="flex-1 overflow-hidden" suppressHydrationWarning>
        {children}
      </main>
    </div>
  );
}
