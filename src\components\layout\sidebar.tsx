'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { cn } from '@/lib/utils';
import {
  FolderIcon,
  CalendarIcon,
  ClockIcon,
  ChartBarIcon,
  PlusIcon,
  HomeIcon,
  Cog6ToothIcon,
  CubeIcon
} from '@heroicons/react/24/outline';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { apiClient } from '@/lib/api/client';
import { initials } from '@/lib/utils';

interface User {
  id: string;
  name: string;
  email: string;
  avatar?: string;
}

interface Folder {
  id: string;
  name: string;
}

interface Project {
  id: string;
  name: string;
  color: string;
  folderId?: string;
  folder?: Folder;
}

interface NavigationItem {
  name: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
}

const navigation: NavigationItem[] = [
  { name: 'Dashboard', href: '/', icon: HomeIcon },
  { name: 'Projects', href: '/projects', icon: FolderIcon },
  { name: 'Calendar', href: '/calendar', icon: CalendarIcon },
  { name: 'Time Tracking', href: '/time-tracking', icon: ClockIcon },
  { name: 'Reports', href: '/reports', icon: ChartBarIcon },
  { name: 'Settings', href: '/settings', icon: Cog6ToothIcon },
];

export function Sidebar() {
  const pathname = usePathname();
  const [users, setUsers] = useState<User[]>([]);
  const [projects, setProjects] = useState<Project[]>([]);
  const [folders, setFolders] = useState<Folder[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  const currentUser = users[0];

  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      try {
        const [usersData, projectsData] = await Promise.all([
          apiClient.getUsers() as Promise<User[]>,
          apiClient.getProjects() as Promise<Project[]>,
        ]);

        setUsers(usersData);
        
        if (Array.isArray(projectsData)) {
          setProjects(projectsData);
          
          // Extract unique folders from projects
          const folderMap = new Map<string, Folder>();
          
          projectsData.forEach((project) => {
            if (project.folder) {
              folderMap.set(project.folder.id, project.folder);
            }
          });
          
          setFolders(Array.from(folderMap.values()));
        }
      } catch (error) {
        console.error('Error fetching sidebar data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, []);

  return (
    <div className="flex h-full w-64 flex-col bg-card border-r" suppressHydrationWarning>
      {/* Logo and User */}
      <div className="flex h-16 items-center justify-between px-4 border-b">
        <div className="flex items-center space-x-2">
          <div className="h-8 w-8 rounded-lg bg-primary flex items-center justify-center">
            <CubeIcon className="h-5 w-5 text-primary-foreground" />
          </div>
          <span className="font-semibold text-lg">Shakil AI Project Management</span>
        </div>
      </div>

      {/* Navigation */}
      <nav className="flex-1 space-y-1 px-2 py-4">
        {navigation.map((item) => {
          const isActive = pathname === item.href;
          return (
            <Link
              key={item.name}
              href={item.href}
              className={cn(
                'group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors',
                isActive
                  ? 'bg-primary text-primary-foreground'
                  : 'text-muted-foreground hover:bg-accent hover:text-accent-foreground'
              )}
            >
              <item.icon
                className={cn(
                  'mr-3 h-5 w-5 flex-shrink-0',
                  isActive ? 'text-primary-foreground' : 'text-muted-foreground'
                )}
                aria-hidden="true"
              />
              {item.name}
            </Link>
          );
        })}
      </nav>

      {/* Projects Section */}
      <div className="px-2 py-4 border-t">
        <div className="flex items-center justify-between mb-3">
          <h3 className="text-sm font-medium text-muted-foreground">Projects</h3>
          <Button size="icon" variant="ghost" className="h-6 w-6">
            <PlusIcon className="h-4 w-4" />
          </Button>
        </div>
        
        {/* Folders */}
        <div className="space-y-1">
          {isLoading ? (
            <div className="px-2 py-1">
              <div className="h-4 bg-muted rounded w-3/4 mb-2"></div>
              <div className="ml-4 space-y-1">
                <div className="h-3 bg-muted rounded w-1/2"></div>
              </div>
            </div>
          ) : (
            <>
              {folders.length > 0 && folders.map((folder) => (
                <div key={folder.id} className="space-y-1">
                  <div className="flex items-center px-2 py-1 text-sm text-muted-foreground">
                    <FolderIcon className="mr-2 h-4 w-4" />
                    <span className="truncate">{folder.name}</span>
                  </div>

                  {/* Projects in folder */}
                  {projects
                    .filter(project => project.folderId === folder.id)
                    .map((project) => (
                      <Link
                        key={project.id}
                        href={`/projects/${project.id}`}
                        className="flex items-center px-4 py-1 text-sm text-muted-foreground hover:bg-accent hover:text-accent-foreground rounded-md ml-4"
                      >
                        <div 
                          className="mr-2 h-2 w-2 rounded-full"
                          style={{ backgroundColor: project.color }}
                        />
                        <span className="truncate">{project.name}</span>
                      </Link>
                    ))}
                </div>
              ))}
            </>
          )}

          {/* Projects without folder */}
          {isLoading ? (
            <div className="px-2 py-1">
              <div className="h-4 bg-muted rounded w-1/2 mb-1"></div>
              <div className="h-3 bg-muted rounded w-2/3"></div>
            </div>
          ) : projects
            .filter(project => !project.folderId)
            .map((project) => (
              <Link
                key={project.id}
                href={`/projects/${project.id}`}
                className="flex items-center px-2 py-1 text-sm text-muted-foreground hover:bg-accent hover:text-accent-foreground rounded-md"
              >
                <div
                  className="mr-2 h-2 w-2 rounded-full"
                  style={{ backgroundColor: project.color }}
                />
                <span className="truncate">{project.name}</span>
              </Link>
            ))}
        </div>
      </div>

      {/* User Profile */}
      <div className="border-t p-4">
        {currentUser ? (
          <div className="flex items-center space-x-3">
            <Avatar className="h-8 w-8">
              <AvatarImage src={currentUser.avatar} alt={currentUser.name} />
              <AvatarFallback>{initials(currentUser.name)}</AvatarFallback>
            </Avatar>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium truncate">{currentUser.name}</p>
              <p className="text-xs text-muted-foreground truncate">{currentUser.email}</p>
            </div>
          </div>
        ) : (
          <div className="animate-pulse">
            <div className="flex items-center space-x-3">
              <div className="h-8 w-8 bg-muted rounded-full"></div>
              <div className="flex-1 space-y-1">
                <div className="h-3 bg-muted rounded w-3/4"></div>
                <div className="h-2 bg-muted rounded w-1/2"></div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
