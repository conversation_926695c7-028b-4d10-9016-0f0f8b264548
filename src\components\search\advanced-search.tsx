'use client';

import React, { useState, useEffect, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  MagnifyingGlassIcon,
  FunnelIcon,
  XMarkIcon,
  BookmarkIcon,
  ClockIcon,
  UserIcon,
  TagIcon,
} from '@heroicons/react/24/outline';
import { cn } from '@/lib/utils';

export interface SearchFilter {
  id: string;
  type: 'text' | 'select' | 'date' | 'user' | 'tag' | 'priority' | 'status';
  label: string;
  value: any;
  options?: Array<{ label: string; value: any }>;
}

export interface SavedSearch {
  id: string;
  name: string;
  query: string;
  filters: SearchFilter[];
  createdAt: Date;
  isDefault?: boolean;
}

interface AdvancedSearchProps {
  onSearch: (query: string, filters: SearchFilter[]) => void;
  availableFilters: Omit<SearchFilter, 'value'>[];
  savedSearches?: SavedSearch[];
  onSaveSearch?: (search: Omit<SavedSearch, 'id' | 'createdAt'>) => void;
  onDeleteSearch?: (searchId: string) => void;
  placeholder?: string;
  className?: string;
}

export function AdvancedSearch({
  onSearch,
  availableFilters,
  savedSearches = [],
  onSaveSearch,
  onDeleteSearch,
  placeholder = "Search tasks, projects, comments...",
  className,
}: AdvancedSearchProps) {
  const [query, setQuery] = useState('');
  const [activeFilters, setActiveFilters] = useState<SearchFilter[]>([]);
  const [showFilters, setShowFilters] = useState(false);
  const [showSaveDialog, setShowSaveDialog] = useState(false);
  const [saveSearchName, setSaveSearchName] = useState('');

  // Debounced search
  useEffect(() => {
    const timer = setTimeout(() => {
      onSearch(query, activeFilters);
    }, 300);

    return () => clearTimeout(timer);
  }, [query, activeFilters, onSearch]);

  const addFilter = (filterTemplate: Omit<SearchFilter, 'value'>) => {
    const newFilter: SearchFilter = {
      ...filterTemplate,
      value: filterTemplate.type === 'select' ? filterTemplate.options?.[0]?.value : '',
    };

    setActiveFilters(prev => [...prev, newFilter]);
  };

  const updateFilter = (filterId: string, value: any) => {
    setActiveFilters(prev =>
      prev.map(filter =>
        filter.id === filterId ? { ...filter, value } : filter
      )
    );
  };

  const removeFilter = (filterId: string) => {
    setActiveFilters(prev => prev.filter(filter => filter.id !== filterId));
  };

  const clearAllFilters = () => {
    setQuery('');
    setActiveFilters([]);
  };

  const loadSavedSearch = (savedSearch: SavedSearch) => {
    setQuery(savedSearch.query);
    setActiveFilters(savedSearch.filters);
  };

  const saveCurrentSearch = () => {
    if (!saveSearchName.trim() || !onSaveSearch) return;

    onSaveSearch({
      name: saveSearchName,
      query,
      filters: activeFilters,
    });

    setSaveSearchName('');
    setShowSaveDialog(false);
  };

  const availableFilterOptions = useMemo(() => {
    const usedFilterIds = new Set(activeFilters.map(f => f.id));
    return availableFilters.filter(f => !usedFilterIds.has(f.id));
  }, [availableFilters, activeFilters]);

  const getFilterIcon = (type: string) => {
    switch (type) {
      case 'user': return UserIcon;
      case 'tag': return TagIcon;
      case 'date': return ClockIcon;
      default: return FunnelIcon;
    }
  };

  return (
    <div className={cn('space-y-4', className)}>
      {/* Main Search Bar */}
      <div className="relative">
        <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
        <Input
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          placeholder={placeholder}
          className="pl-10 pr-20"
        />
        <div className="absolute right-2 top-1/2 transform -translate-y-1/2 flex items-center space-x-1">
          <Button
            size="sm"
            variant="ghost"
            onClick={() => setShowFilters(!showFilters)}
            className={cn(
              "h-7 px-2",
              (showFilters || activeFilters.length > 0) && "bg-primary/10 text-primary"
            )}
          >
            <FunnelIcon className="h-3 w-3 mr-1" />
            Filters
            {activeFilters.length > 0 && (
              <Badge variant="secondary" className="ml-1 h-4 text-xs">
                {activeFilters.length}
              </Badge>
            )}
          </Button>
        </div>
      </div>

      {/* Active Filters */}
      {activeFilters.length > 0 && (
        <div className="flex flex-wrap gap-2">
          {activeFilters.map((filter) => {
            const FilterIcon = getFilterIcon(filter.type);
            return (
              <Badge key={filter.id} variant="outline" className="flex items-center space-x-1 pr-1">
                <FilterIcon className="h-3 w-3" />
                <span className="text-xs">{filter.label}:</span>
                <span className="text-xs font-medium">
                  {filter.type === 'select' 
                    ? filter.options?.find(opt => opt.value === filter.value)?.label || filter.value
                    : filter.value
                  }
                </span>
                <Button
                  size="icon"
                  variant="ghost"
                  className="h-4 w-4 p-0 hover:bg-destructive hover:text-destructive-foreground"
                  onClick={() => removeFilter(filter.id)}
                >
                  <XMarkIcon className="h-3 w-3" />
                </Button>
              </Badge>
            );
          })}
          <Button
            size="sm"
            variant="ghost"
            onClick={clearAllFilters}
            className="h-6 px-2 text-xs text-muted-foreground hover:text-destructive"
          >
            Clear all
          </Button>
        </div>
      )}

      {/* Filter Panel */}
      {showFilters && (
        <Card>
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="text-sm">Advanced Filters</CardTitle>
              <div className="flex items-center space-x-2">
                {(query || activeFilters.length > 0) && onSaveSearch && (
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => setShowSaveDialog(true)}
                  >
                    <BookmarkIcon className="h-3 w-3 mr-1" />
                    Save Search
                  </Button>
                )}
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => setShowFilters(false)}
                >
                  <XMarkIcon className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Available Filters */}
            {availableFilterOptions.length > 0 && (
              <div>
                <h4 className="text-xs font-medium text-muted-foreground mb-2">Add Filter</h4>
                <div className="flex flex-wrap gap-2">
                  {availableFilterOptions.map((filter) => {
                    const FilterIcon = getFilterIcon(filter.type);
                    return (
                      <Button
                        key={filter.id}
                        size="sm"
                        variant="outline"
                        onClick={() => addFilter(filter)}
                        className="h-7 text-xs"
                      >
                        <FilterIcon className="h-3 w-3 mr-1" />
                        {filter.label}
                      </Button>
                    );
                  })}
                </div>
              </div>
            )}

            {/* Active Filter Controls */}
            {activeFilters.length > 0 && (
              <div>
                <h4 className="text-xs font-medium text-muted-foreground mb-2">Active Filters</h4>
                <div className="space-y-2">
                  {activeFilters.map((filter) => (
                    <div key={filter.id} className="flex items-center space-x-2">
                      <span className="text-xs font-medium min-w-0 flex-shrink-0">
                        {filter.label}:
                      </span>
                      {filter.type === 'select' ? (
                        <select
                          value={filter.value}
                          onChange={(e) => updateFilter(filter.id, e.target.value)}
                          className="flex-1 text-xs border rounded px-2 py-1"
                        >
                          {filter.options?.map((option) => (
                            <option key={option.value} value={option.value}>
                              {option.label}
                            </option>
                          ))}
                        </select>
                      ) : filter.type === 'date' ? (
                        <Input
                          type="date"
                          value={filter.value}
                          onChange={(e) => updateFilter(filter.id, e.target.value)}
                          className="flex-1 text-xs h-7"
                        />
                      ) : (
                        <Input
                          value={filter.value}
                          onChange={(e) => updateFilter(filter.id, e.target.value)}
                          className="flex-1 text-xs h-7"
                          placeholder={`Enter ${filter.label.toLowerCase()}`}
                        />
                      )}
                      <Button
                        size="icon"
                        variant="ghost"
                        className="h-7 w-7"
                        onClick={() => removeFilter(filter.id)}
                      >
                        <XMarkIcon className="h-3 w-3" />
                      </Button>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Saved Searches */}
      {savedSearches.length > 0 && (
        <div>
          <h4 className="text-xs font-medium text-muted-foreground mb-2">Saved Searches</h4>
          <div className="flex flex-wrap gap-2">
            {savedSearches.map((savedSearch) => (
              <div key={savedSearch.id} className="flex items-center">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => loadSavedSearch(savedSearch)}
                  className="h-7 text-xs pr-1"
                >
                  <BookmarkIcon className="h-3 w-3 mr-1" />
                  {savedSearch.name}
                  {savedSearch.isDefault && (
                    <Badge variant="secondary" className="ml-1 h-3 text-xs">
                      Default
                    </Badge>
                  )}
                </Button>
                {onDeleteSearch && (
                  <Button
                    size="icon"
                    variant="ghost"
                    className="h-7 w-7 -ml-1"
                    onClick={() => onDeleteSearch(savedSearch.id)}
                  >
                    <XMarkIcon className="h-3 w-3" />
                  </Button>
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Save Search Dialog */}
      {showSaveDialog && (
        <Card>
          <CardContent className="p-4">
            <div className="space-y-3">
              <h4 className="text-sm font-medium">Save Current Search</h4>
              <Input
                value={saveSearchName}
                onChange={(e) => setSaveSearchName(e.target.value)}
                placeholder="Enter search name"
                className="text-sm"
              />
              <div className="flex justify-end space-x-2">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => setShowSaveDialog(false)}
                >
                  Cancel
                </Button>
                <Button
                  size="sm"
                  onClick={saveCurrentSearch}
                  disabled={!saveSearchName.trim()}
                >
                  Save
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
