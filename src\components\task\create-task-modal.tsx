'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { XMarkIcon } from '@heroicons/react/24/outline';
import { apiClient } from '@/lib/api/client';

interface CreateTaskModalProps {
  isOpen: boolean;
  onClose: () => void;
  onTaskCreated?: (task: any) => void;
  projectId?: string;
}

export function CreateTaskModal({ isOpen, onClose, onTaskCreated, projectId }: CreateTaskModalProps) {
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    projectId: projectId || '',
    assigneeId: '',
    priority: 'medium',
    dueDate: '',
    estimatedTime: '',
  });
  const [projects, setProjects] = useState<any[]>([]);
  const [users, setUsers] = useState<any[]>([]);
  const [taskStatuses, setTaskStatuses] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);

  useEffect(() => {
    if (isOpen) {
      fetchData();
    }
  }, [isOpen]);

  const fetchData = async () => {
    setLoading(true);
    try {
      const [projectsData, usersData, statusesData] = await Promise.all([
        apiClient.getProjects().catch(() => [
          { id: '1', name: 'Demo Project', description: 'Sample project', color: '#3b82f6' }
        ]),
        apiClient.getUsers().catch(() => [
          { id: '1', name: 'Demo User', email: '<EMAIL>', avatar: '' }
        ]),
        apiClient.getTaskStatuses().catch(() => [
          { id: 'status-1', name: 'To Do', color: '#6b7280', position: 0 },
          { id: 'status-2', name: 'In Progress', color: '#3b82f6', position: 1 },
          { id: 'status-3', name: 'Review', color: '#f59e0b', position: 2 },
          { id: 'status-4', name: 'Done', color: '#22c55e', position: 3 },
        ]),
      ]);

      setProjects(projectsData);
      setUsers(usersData);
      setTaskStatuses(statusesData);
    } catch (error) {
      console.error('Error fetching create task data:', error);
      // Set fallback data
      setProjects([{ id: '1', name: 'Demo Project', description: 'Sample project', color: '#3b82f6' }]);
      setUsers([{ id: '1', name: 'Demo User', email: '<EMAIL>', avatar: '' }]);
      setTaskStatuses([
        { id: 'status-1', name: 'To Do', color: '#6b7280', position: 0 },
        { id: 'status-2', name: 'In Progress', color: '#3b82f6', position: 1 },
        { id: 'status-3', name: 'Review', color: '#f59e0b', position: 2 },
        { id: 'status-4', name: 'Done', color: '#22c55e', position: 3 },
      ]);
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.title || !formData.projectId) {
      alert('Title and project are required');
      return;
    }

    setSubmitting(true);
    try {
      const defaultStatus = taskStatuses.find(s => s.isDefault) || taskStatuses[0];
      const currentUser = users[0]; // For demo, use first user as creator
      
      const taskData = {
        title: formData.title,
        description: formData.description,
        projectId: formData.projectId,
        assigneeId: formData.assigneeId || null,
        creatorId: currentUser?.id,
        statusId: defaultStatus?.id,
        priority: formData.priority,
        dueDate: formData.dueDate ? new Date(formData.dueDate).toISOString() : null,
        estimatedTime: formData.estimatedTime ? parseInt(formData.estimatedTime) : null,
      };

      const newTask = await apiClient.createTask(taskData);
      
      onTaskCreated?.(newTask);
      onClose();
      
      // Reset form
      setFormData({
        title: '',
        description: '',
        projectId: projectId || '',
        assigneeId: '',
        priority: 'medium',
        dueDate: '',
        estimatedTime: '',
      });
    } catch (error) {
      console.error('Error creating task:', error);
      alert('Failed to create task. Please try again.');
    } finally {
      setSubmitting(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="bg-background rounded-lg shadow-lg w-full max-w-2xl max-h-[90vh] overflow-hidden">
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="text-xl font-semibold">Create New Task</h2>
          <Button variant="ghost" size="icon" onClick={onClose}>
            <XMarkIcon className="h-5 w-5" />
          </Button>
        </div>

        {loading ? (
          <div className="p-6 text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">Loading...</p>
          </div>
        ) : (
          <form onSubmit={handleSubmit} className="p-6 space-y-4">
            <div>
              <label className="text-sm font-medium">Title *</label>
              <Input
                value={formData.title}
                onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                placeholder="Enter task title"
                required
              />
            </div>

            <div>
              <label className="text-sm font-medium">Description</label>
              <textarea
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                placeholder="Enter task description"
                className="w-full p-3 border rounded-md resize-none"
                rows={3}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium">Project *</label>
                <select
                  value={formData.projectId}
                  onChange={(e) => setFormData({ ...formData, projectId: e.target.value })}
                  className="w-full p-2 border rounded-md"
                  required
                >
                  <option value="">Select project</option>
                  {projects.map((project) => (
                    <option key={project.id} value={project.id}>
                      {project.name}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="text-sm font-medium">Assignee</label>
                <select
                  value={formData.assigneeId}
                  onChange={(e) => setFormData({ ...formData, assigneeId: e.target.value })}
                  className="w-full p-2 border rounded-md"
                >
                  <option value="">Unassigned</option>
                  {users.map((user) => (
                    <option key={user.id} value={user.id}>
                      {user.name}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium">Priority</label>
                <select
                  value={formData.priority}
                  onChange={(e) => setFormData({ ...formData, priority: e.target.value })}
                  className="w-full p-2 border rounded-md"
                >
                  <option value="low">Low</option>
                  <option value="medium">Medium</option>
                  <option value="high">High</option>
                  <option value="urgent">Urgent</option>
                </select>
              </div>

              <div>
                <label className="text-sm font-medium">Due Date</label>
                <Input
                  type="date"
                  value={formData.dueDate}
                  onChange={(e) => setFormData({ ...formData, dueDate: e.target.value })}
                />
              </div>
            </div>

            <div>
              <label className="text-sm font-medium">Estimated Time (minutes)</label>
              <Input
                type="number"
                value={formData.estimatedTime}
                onChange={(e) => setFormData({ ...formData, estimatedTime: e.target.value })}
                placeholder="e.g., 120"
              />
            </div>

            <div className="flex justify-end space-x-2 pt-4">
              <Button type="button" variant="outline" onClick={onClose}>
                Cancel
              </Button>
              <Button type="submit" disabled={submitting}>
                {submitting ? 'Creating...' : 'Create Task'}
              </Button>
            </div>
          </form>
        )}
      </div>
    </div>
  );
}
