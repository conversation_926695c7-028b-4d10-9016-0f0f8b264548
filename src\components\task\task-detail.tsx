'use client';

import React, { useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
  XMarkIcon,
  CalendarIcon,
  ClockIcon,
  UserIcon,
  TagIcon,
  ChatBubbleLeftIcon,
  PaperClipIcon,
  PlayIcon
} from '@heroicons/react/24/outline';
import { Task, Comment } from '@/types';
import { 
  getUserById, 
  getProjectById, 
  mockTaskStatuses,
  mockComments 
} from '@/lib/mock-data';
import { 
  formatRelativeDate, 
  formatDuration, 
  initials, 
  getPriorityColor 
} from '@/lib/utils';

interface TaskDetailProps {
  task: Task;
  onClose: () => void;
  onUpdate?: (task: Task) => void;
}

export function TaskDetail({ task, onClose, onUpdate }: TaskDetailProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [editedTask, setEditedTask] = useState(task);
  const [newComment, setNewComment] = useState('');

  const assignee = task.assigneeId ? getUserById(task.assigneeId) : null;
  const creator = getUserById(task.creatorId);
  const project = getProjectById(task.projectId);
  const status = mockTaskStatuses.find(s => s.id === task.status);
  const taskComments = mockComments.filter(c => c.taskId === task.id);

  const handleSave = () => {
    onUpdate?.(editedTask);
    setIsEditing(false);
  };

  const handleAddComment = () => {
    if (newComment.trim()) {
      // In a real app, this would make an API call
      console.log('Adding comment:', newComment);
      setNewComment('');
    }
  };

  const startTimer = () => {
    // In a real app, this would start the time tracker
    console.log('Starting timer for task:', task.id);
  };

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="bg-background rounded-lg shadow-lg w-full max-w-4xl max-h-[90vh] overflow-hidden">
        <div className="flex h-full">
          {/* Main Content */}
          <div className="flex-1 flex flex-col">
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b">
              <div className="flex items-center space-x-3">
                <div 
                  className="h-4 w-4 rounded-full" 
                  style={{ backgroundColor: project?.color }}
                />
                <span className="text-sm text-muted-foreground">{project?.name}</span>
              </div>
              <Button variant="ghost" size="icon" onClick={onClose}>
                <XMarkIcon className="h-5 w-5" />
              </Button>
            </div>

            {/* Task Content */}
            <div className="flex-1 overflow-auto p-6">
              <div className="space-y-6">
                {/* Title and Description */}
                <div>
                  {isEditing ? (
                    <Input
                      value={editedTask.title}
                      onChange={(e) => setEditedTask({ ...editedTask, title: e.target.value })}
                      className="text-xl font-semibold mb-3"
                    />
                  ) : (
                    <h1 className="text-2xl font-semibold mb-3">{task.title}</h1>
                  )}
                  
                  {isEditing ? (
                    <textarea
                      value={editedTask.description || ''}
                      onChange={(e) => setEditedTask({ ...editedTask, description: e.target.value })}
                      className="w-full p-3 border rounded-md resize-none"
                      rows={4}
                      placeholder="Add a description..."
                    />
                  ) : (
                    <p className="text-muted-foreground">
                      {task.description || 'No description provided.'}
                    </p>
                  )}
                </div>

                {/* Task Properties */}
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-3">
                    <div className="flex items-center space-x-2">
                      <UserIcon className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm font-medium">Assignee</span>
                    </div>
                    {assignee ? (
                      <div className="flex items-center space-x-2">
                        <Avatar className="h-6 w-6">
                          <AvatarImage src={assignee.avatar} alt={assignee.name} />
                          <AvatarFallback className="text-xs">
                            {initials(assignee.name)}
                          </AvatarFallback>
                        </Avatar>
                        <span className="text-sm">{assignee.name}</span>
                      </div>
                    ) : (
                      <span className="text-sm text-muted-foreground">Unassigned</span>
                    )}
                  </div>

                  <div className="space-y-3">
                    <div className="flex items-center space-x-2">
                      <TagIcon className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm font-medium">Status</span>
                    </div>
                    <Badge 
                      variant="outline"
                      style={{ 
                        borderColor: status?.color,
                        color: status?.color
                      }}
                    >
                      {status?.name}
                    </Badge>
                  </div>

                  <div className="space-y-3">
                    <div className="flex items-center space-x-2">
                      <CalendarIcon className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm font-medium">Due Date</span>
                    </div>
                    <span className="text-sm">
                      {task.dueDate ? formatRelativeDate(task.dueDate) : 'No due date'}
                    </span>
                  </div>

                  <div className="space-y-3">
                    <div className="flex items-center space-x-2">
                      <ClockIcon className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm font-medium">Time Estimate</span>
                    </div>
                    <span className="text-sm">
                      {task.estimatedTime ? formatDuration(task.estimatedTime) : 'No estimate'}
                    </span>
                  </div>
                </div>

                {/* Priority */}
                <div>
                  <div className="flex items-center space-x-2 mb-2">
                    <span className="text-sm font-medium">Priority</span>
                  </div>
                  <Badge 
                    variant="outline"
                    style={{ 
                      borderColor: getPriorityColor(task.priority),
                      color: getPriorityColor(task.priority)
                    }}
                  >
                    {task.priority}
                  </Badge>
                </div>

                {/* Tags */}
                {task.tags.length > 0 && (
                  <div>
                    <div className="flex items-center space-x-2 mb-2">
                      <TagIcon className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm font-medium">Tags</span>
                    </div>
                    <div className="flex flex-wrap gap-2">
                      {task.tags.map((tagId) => (
                        <Badge key={tagId} variant="secondary">
                          Tag {tagId.split('-')[1]}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}

                {/* Time Tracking */}
                <div>
                  <div className="flex items-center justify-between mb-3">
                    <span className="text-sm font-medium">Time Tracking</span>
                    <Button size="sm" onClick={startTimer}>
                      <PlayIcon className="mr-2 h-4 w-4" />
                      Start Timer
                    </Button>
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {task.actualTime ? (
                      <span>Logged: {formatDuration(task.actualTime)}</span>
                    ) : (
                      <span>No time logged yet</span>
                    )}
                  </div>
                </div>

                {/* Comments */}
                <div>
                  <div className="flex items-center space-x-2 mb-4">
                    <ChatBubbleLeftIcon className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm font-medium">Comments ({taskComments.length})</span>
                  </div>
                  
                  <div className="space-y-4">
                    {taskComments.map((comment) => {
                      const commentUser = getUserById(comment.userId);
                      return (
                        <div key={comment.id} className="flex space-x-3">
                          <Avatar className="h-8 w-8">
                            <AvatarImage src={commentUser?.avatar} alt={commentUser?.name} />
                            <AvatarFallback className="text-xs">
                              {commentUser ? initials(commentUser.name) : '?'}
                            </AvatarFallback>
                          </Avatar>
                          <div className="flex-1">
                            <div className="flex items-center space-x-2 mb-1">
                              <span className="text-sm font-medium">{commentUser?.name}</span>
                              <span className="text-xs text-muted-foreground">
                                {formatRelativeDate(comment.createdAt)}
                              </span>
                            </div>
                            <p className="text-sm">{comment.content}</p>
                          </div>
                        </div>
                      );
                    })}
                  </div>

                  {/* Add Comment */}
                  <div className="mt-4 space-y-2">
                    <textarea
                      value={newComment}
                      onChange={(e) => setNewComment(e.target.value)}
                      placeholder="Add a comment..."
                      className="w-full p-3 border rounded-md resize-none"
                      rows={3}
                    />
                    <div className="flex justify-end">
                      <Button size="sm" onClick={handleAddComment}>
                        Add Comment
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Footer */}
            <div className="border-t p-6">
              <div className="flex items-center justify-between">
                <div className="text-xs text-muted-foreground">
                  Created by {creator?.name} • {formatRelativeDate(task.createdAt)}
                </div>
                <div className="flex space-x-2">
                  {isEditing ? (
                    <>
                      <Button variant="outline" onClick={() => setIsEditing(false)}>
                        Cancel
                      </Button>
                      <Button onClick={handleSave}>
                        Save Changes
                      </Button>
                    </>
                  ) : (
                    <Button onClick={() => setIsEditing(true)}>
                      Edit Task
                    </Button>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
