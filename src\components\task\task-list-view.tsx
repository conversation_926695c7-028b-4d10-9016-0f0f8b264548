'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Checkbox } from '@/components/ui/checkbox';
import {
  PlusIcon,
  MagnifyingGlassIcon,
  CalendarIcon,
  UserIcon,
  FlagIcon,
  CheckCircleIcon,
  ClockIcon,
  ChatBubbleLeftIcon,
  PaperClipIcon,
} from '@heroicons/react/24/outline';
import { apiClient } from '@/lib/api/client';
import { formatDate, getPriorityColor, initials } from '@/lib/utils';

interface TaskListViewProps {
  projectId: string;
  onTaskClick?: (task: any) => void;
  onCreateTask?: () => void;
}

export function TaskListView({ projectId, onTaskClick, onCreateTask }: TaskListViewProps) {
  const [tasks, setTasks] = useState<any[]>([]);
  const [users, setUsers] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedTasks, setSelectedTasks] = useState<Set<string>>(new Set());
  const [sortBy, setSortBy] = useState<'priority' | 'dueDate' | 'status' | 'assignee'>('priority');
  const [filterStatus, setFilterStatus] = useState<string>('all');

  useEffect(() => {
    fetchData();
  }, [projectId]);

  const fetchData = async () => {
    try {
      setLoading(true);
      const [tasksData, usersData] = await Promise.all([
        apiClient.getTasks({ projectId }).catch(() => {
          // Fallback to mock tasks if API fails
          return [
            {
              id: '1',
              title: 'Design System Implementation',
              description: 'Create comprehensive design system for the application',
              priority: 'high',
              dueDate: new Date(Date.now() + 86400000).toISOString(),
              assigneeId: '1',
              projectId,
              status: { isCompleted: false },
              commentsCount: 3,
              attachmentsCount: 2,
            },
            {
              id: '2',
              title: 'API Integration Testing',
              description: 'Test all API endpoints and error handling',
              priority: 'urgent',
              dueDate: new Date().toISOString(),
              assigneeId: '2',
              projectId,
              status: { isCompleted: false },
              commentsCount: 1,
              attachmentsCount: 0,
            },
            {
              id: '3',
              title: 'User Documentation',
              description: 'Write comprehensive user documentation',
              priority: 'medium',
              dueDate: new Date(Date.now() + 172800000).toISOString(),
              assigneeId: '1',
              projectId,
              status: { isCompleted: true },
              commentsCount: 0,
              attachmentsCount: 1,
            },
          ];
        }),
        apiClient.getUsers().catch(() => {
          // Fallback to mock users if API fails
          return [
            {
              id: '1',
              name: 'Alice Johnson',
              email: '<EMAIL>',
              avatar: '',
            },
            {
              id: '2',
              name: 'Bob Smith',
              email: '<EMAIL>',
              avatar: '',
            },
          ];
        }),
      ]);

      setTasks(tasksData as any[]);
      setUsers(usersData as any[]);
    } catch (error) {
      console.error('Error fetching list data:', error);
      // Set fallback data on complete failure
      setTasks([]);
      setUsers([]);
    } finally {
      setLoading(false);
    }
  };

  const getUserById = (userId: string) => users.find(user => user.id === userId);

  const getStatusInfo = (task: any) => {
    // Mock status info based on task properties
    if (task.status?.isCompleted || task.completed) {
      return { name: 'Completed', color: '#22c55e', isCompleted: true };
    }
    if (task.status?.name) {
      return task.status;
    }
    // Default statuses
    const statuses = [
      { name: 'To Do', color: '#6b7280' },
      { name: 'In Progress', color: '#3b82f6' },
      { name: 'Review', color: '#f59e0b' },
      { name: 'Done', color: '#22c55e' },
    ];
    return statuses[Math.floor(Math.random() * statuses.length)];
  };

  const filteredAndSortedTasks = tasks
    .filter(task => {
      const matchesSearch = task.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           task.description?.toLowerCase().includes(searchQuery.toLowerCase());
      const matchesStatus = filterStatus === 'all' || 
                           (filterStatus === 'completed' && (task.status?.isCompleted || task.completed)) ||
                           (filterStatus === 'active' && !(task.status?.isCompleted || task.completed));
      return matchesSearch && matchesStatus;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'priority':
          const priorityOrder = { urgent: 4, high: 3, medium: 2, low: 1 };
          return (priorityOrder[b.priority as keyof typeof priorityOrder] || 0) - 
                 (priorityOrder[a.priority as keyof typeof priorityOrder] || 0);
        case 'dueDate':
          if (!a.dueDate && !b.dueDate) return 0;
          if (!a.dueDate) return 1;
          if (!b.dueDate) return -1;
          return new Date(a.dueDate).getTime() - new Date(b.dueDate).getTime();
        case 'assignee':
          const userA = getUserById(a.assigneeId);
          const userB = getUserById(b.assigneeId);
          return (userA?.name || '').localeCompare(userB?.name || '');
        default:
          return 0;
      }
    });

  const handleTaskSelect = (taskId: string, checked: boolean) => {
    const newSelected = new Set(selectedTasks);
    if (checked) {
      newSelected.add(taskId);
    } else {
      newSelected.delete(taskId);
    }
    setSelectedTasks(newSelected);
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedTasks(new Set(filteredAndSortedTasks.map(task => task.id)));
    } else {
      setSelectedTasks(new Set());
    }
  };

  const isOverdue = (dueDate: string) => {
    return new Date(dueDate) < new Date() && new Date(dueDate).toDateString() !== new Date().toDateString();
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Header Controls */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="relative">
            <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search tasks..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 w-64"
            />
          </div>
          
          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value as any)}
            className="px-3 py-2 border rounded-md text-sm"
          >
            <option value="priority">Sort by Priority</option>
            <option value="dueDate">Sort by Due Date</option>
            <option value="assignee">Sort by Assignee</option>
            <option value="status">Sort by Status</option>
          </select>

          <select
            value={filterStatus}
            onChange={(e) => setFilterStatus(e.target.value)}
            className="px-3 py-2 border rounded-md text-sm"
          >
            <option value="all">All Tasks</option>
            <option value="active">Active Tasks</option>
            <option value="completed">Completed Tasks</option>
          </select>
        </div>

        <Button onClick={onCreateTask}>
          <PlusIcon className="mr-2 h-4 w-4" />
          New Task
        </Button>
      </div>

      {/* Task List */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Tasks ({filteredAndSortedTasks.length})</CardTitle>
            <div className="flex items-center space-x-2">
              <Checkbox
                checked={selectedTasks.size === filteredAndSortedTasks.length && filteredAndSortedTasks.length > 0}
                onCheckedChange={handleSelectAll}
              />
              <span className="text-sm text-muted-foreground">
                {selectedTasks.size > 0 && `${selectedTasks.size} selected`}
              </span>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            {filteredAndSortedTasks.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                <CheckCircleIcon className="h-12 w-12 mx-auto mb-4" />
                <p>No tasks found</p>
                <p className="text-sm">Try adjusting your search or filters</p>
              </div>
            ) : (
              filteredAndSortedTasks.map((task) => {
                const assignee = getUserById(task.assigneeId);
                const status = getStatusInfo(task);
                const isSelected = selectedTasks.has(task.id);
                const taskOverdue = task.dueDate && isOverdue(task.dueDate);

                return (
                  <div
                    key={task.id}
                    className={`flex items-center space-x-4 p-4 rounded-lg border transition-colors cursor-pointer hover:bg-accent ${
                      isSelected ? 'bg-accent border-primary' : ''
                    }`}
                    onClick={() => onTaskClick?.(task)}
                  >
                    <Checkbox
                      checked={isSelected}
                      onCheckedChange={(checked) => handleTaskSelect(task.id, checked as boolean)}
                      onClick={(e) => e.stopPropagation()}
                    />

                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2 mb-1">
                        <h3 className={`font-medium truncate ${status.isCompleted ? 'line-through text-muted-foreground' : ''}`}>
                          {task.title}
                        </h3>
                        <Badge
                          variant="outline"
                          style={{
                            borderColor: getPriorityColor(task.priority),
                            color: getPriorityColor(task.priority),
                          }}
                        >
                          {task.priority}
                        </Badge>
                      </div>
                      
                      {task.description && (
                        <p className="text-sm text-muted-foreground truncate mb-2">
                          {task.description}
                        </p>
                      )}

                      <div className="flex items-center space-x-4 text-xs text-muted-foreground">
                        <div className="flex items-center space-x-1">
                          <div
                            className="w-2 h-2 rounded-full"
                            style={{ backgroundColor: status.color }}
                          />
                          <span>{status.name}</span>
                        </div>

                        {task.dueDate && (
                          <div className={`flex items-center space-x-1 ${taskOverdue ? 'text-red-500' : ''}`}>
                            <CalendarIcon className="h-3 w-3" />
                            <span>{formatDate(task.dueDate)}</span>
                          </div>
                        )}

                        {assignee && (
                          <div className="flex items-center space-x-1">
                            <UserIcon className="h-3 w-3" />
                            <span>{assignee.name}</span>
                          </div>
                        )}

                        {task.commentsCount > 0 && (
                          <div className="flex items-center space-x-1">
                            <ChatBubbleLeftIcon className="h-3 w-3" />
                            <span>{task.commentsCount}</span>
                          </div>
                        )}

                        {task.attachmentsCount > 0 && (
                          <div className="flex items-center space-x-1">
                            <PaperClipIcon className="h-3 w-3" />
                            <span>{task.attachmentsCount}</span>
                          </div>
                        )}
                      </div>
                    </div>

                    {assignee && (
                      <Avatar className="h-8 w-8">
                        <AvatarImage src={assignee.avatar} alt={assignee.name} />
                        <AvatarFallback>{initials(assignee.name)}</AvatarFallback>
                      </Avatar>
                    )}
                  </div>
                );
              })
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
