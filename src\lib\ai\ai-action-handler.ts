import { AIProjectInsight } from './groq-service';

export interface AIAction {
  id: string;
  type: 'create_task' | 'schedule_meeting' | 'send_notification' | 'update_status' | 'reassign_task' | 'adjust_deadline' | 'create_milestone' | 'generate_report';
  title: string;
  description: string;
  data: any;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  estimatedTime?: number; // in minutes
}

export interface AIActionResult {
  success: boolean;
  message: string;
  actions?: AIAction[];
  data?: any;
}

export class AIActionHandler {
  async processInsightAction(insight: AIProjectInsight, projectData?: any): Promise<AIActionResult> {
    try {
      console.log('Processing AI insight action:', insight);

      switch (insight.type) {
        case 'risk':
          return this.handleRiskInsight(insight, projectData);
        case 'opportunity':
          return this.handleOpportunityInsight(insight, projectData);
        case 'recommendation':
          return this.handleRecommendationInsight(insight, projectData);
        case 'milestone':
          return this.handleMilestoneInsight(insight, projectData);
        default:
          return this.handleGenericInsight(insight, projectData);
      }
    } catch (error) {
      console.error('Error processing insight action:', error);
      return {
        success: false,
        message: 'Failed to process insight action. Please try again.',
      };
    }
  }

  private async handleRiskInsight(insight: AIProjectInsight, projectData?: any): Promise<AIActionResult> {
    const actions: AIAction[] = [];

    // Generate specific actions based on risk type
    if (insight.title.toLowerCase().includes('overdue')) {
      actions.push({
        id: 'review-overdue-tasks',
        type: 'create_task',
        title: 'Review Overdue Tasks',
        description: 'Conduct a comprehensive review of all overdue tasks and create action plan',
        data: {
          title: 'Review Overdue Tasks',
          description: 'Analyze overdue tasks, identify blockers, and create recovery plan',
          priority: 'urgent',
          estimatedTime: 120,
          tags: ['review', 'urgent', 'ai-generated'],
        },
        priority: 'urgent',
        estimatedTime: 120,
      });

      actions.push({
        id: 'reschedule-tasks',
        type: 'adjust_deadline',
        title: 'Reschedule Critical Tasks',
        description: 'Adjust deadlines for overdue tasks based on current capacity',
        data: {
          action: 'reschedule',
          criteria: 'overdue_tasks',
          strategy: 'realistic_timeline',
        },
        priority: 'high',
        estimatedTime: 60,
      });
    }

    if (insight.title.toLowerCase().includes('workload') || insight.title.toLowerCase().includes('capacity')) {
      actions.push({
        id: 'balance-workload',
        type: 'reassign_task',
        title: 'Balance Team Workload',
        description: 'Redistribute tasks to balance team workload and prevent burnout',
        data: {
          action: 'redistribute',
          strategy: 'workload_balance',
          priority: 'team_capacity',
        },
        priority: 'high',
        estimatedTime: 90,
      });
    }

    return {
      success: true,
      message: `Risk mitigation actions generated for: ${insight.title}`,
      actions,
      data: {
        riskLevel: insight.impact || 'medium',
        confidence: insight.confidence,
        actionItems: insight.actionItems,
      },
    };
  }

  private async handleOpportunityInsight(insight: AIProjectInsight, projectData?: any): Promise<AIActionResult> {
    const actions: AIAction[] = [];

    // Generate opportunity-specific actions
    if (insight.title.toLowerCase().includes('automation') || insight.title.toLowerCase().includes('efficiency')) {
      actions.push({
        id: 'automation-assessment',
        type: 'create_task',
        title: 'Automation Opportunity Assessment',
        description: 'Evaluate and implement automation opportunities identified by AI',
        data: {
          title: 'Automation Opportunity Assessment',
          description: `Assess automation potential: ${insight.description}`,
          priority: 'medium',
          estimatedTime: 180,
          tags: ['automation', 'efficiency', 'ai-suggested'],
        },
        priority: 'medium',
        estimatedTime: 180,
      });
    }

    if (insight.title.toLowerCase().includes('process') || insight.title.toLowerCase().includes('workflow')) {
      actions.push({
        id: 'process-optimization',
        type: 'create_task',
        title: 'Process Optimization Initiative',
        description: 'Implement process improvements suggested by AI analysis',
        data: {
          title: 'Process Optimization Initiative',
          description: `Optimize workflow: ${insight.description}`,
          priority: 'medium',
          estimatedTime: 240,
          tags: ['process', 'optimization', 'ai-suggested'],
        },
        priority: 'medium',
        estimatedTime: 240,
      });
    }

    return {
      success: true,
      message: `Opportunity actions generated for: ${insight.title}`,
      actions,
      data: {
        opportunityType: 'process_improvement',
        confidence: insight.confidence,
        actionItems: insight.actionItems,
      },
    };
  }

  private async handleRecommendationInsight(insight: AIProjectInsight, projectData?: any): Promise<AIActionResult> {
    const actions: AIAction[] = [];

    // Generate recommendation-specific actions
    if (insight.title.toLowerCase().includes('meeting') || insight.title.toLowerCase().includes('sync')) {
      actions.push({
        id: 'schedule-team-sync',
        type: 'schedule_meeting',
        title: 'Schedule Team Sync Meeting',
        description: 'Schedule a team synchronization meeting based on AI recommendation',
        data: {
          meetingType: 'team_sync',
          duration: 60,
          agenda: insight.actionItems,
          priority: 'medium',
        },
        priority: 'medium',
        estimatedTime: 60,
      });
    }

    if (insight.title.toLowerCase().includes('documentation') || insight.title.toLowerCase().includes('knowledge')) {
      actions.push({
        id: 'update-documentation',
        type: 'create_task',
        title: 'Update Project Documentation',
        description: 'Update and improve project documentation based on AI analysis',
        data: {
          title: 'Update Project Documentation',
          description: `Documentation improvement: ${insight.description}`,
          priority: 'low',
          estimatedTime: 120,
          tags: ['documentation', 'knowledge', 'ai-suggested'],
        },
        priority: 'low',
        estimatedTime: 120,
      });
    }

    if (insight.title.toLowerCase().includes('review') || insight.title.toLowerCase().includes('audit')) {
      actions.push({
        id: 'project-health-review',
        type: 'create_task',
        title: 'Project Health Review',
        description: 'Conduct comprehensive project health review based on AI insights',
        data: {
          title: 'Project Health Review',
          description: `Health review: ${insight.description}`,
          priority: 'medium',
          estimatedTime: 90,
          tags: ['review', 'health-check', 'ai-suggested'],
        },
        priority: 'medium',
        estimatedTime: 90,
      });
    }

    return {
      success: true,
      message: `Recommendation actions generated for: ${insight.title}`,
      actions,
      data: {
        recommendationType: 'process_improvement',
        confidence: insight.confidence,
        actionItems: insight.actionItems,
      },
    };
  }

  private async handleMilestoneInsight(insight: AIProjectInsight, projectData?: any): Promise<AIActionResult> {
    const actions: AIAction[] = [];

    actions.push({
      id: 'create-milestone',
      type: 'create_milestone',
      title: 'Create Project Milestone',
      description: 'Create a new project milestone based on AI analysis',
      data: {
        title: insight.title,
        description: insight.description,
        targetDate: this.calculateMilestoneDate(),
        criteria: insight.actionItems,
      },
      priority: 'medium',
      estimatedTime: 30,
    });

    actions.push({
      id: 'milestone-planning',
      type: 'create_task',
      title: 'Milestone Planning Session',
      description: 'Plan and organize tasks for the new milestone',
      data: {
        title: 'Milestone Planning Session',
        description: `Plan milestone: ${insight.title}`,
        priority: 'high',
        estimatedTime: 120,
        tags: ['planning', 'milestone', 'ai-suggested'],
      },
      priority: 'high',
      estimatedTime: 120,
    });

    return {
      success: true,
      message: `Milestone actions generated for: ${insight.title}`,
      actions,
      data: {
        milestoneType: 'project_checkpoint',
        confidence: insight.confidence,
        actionItems: insight.actionItems,
      },
    };
  }

  private async handleGenericInsight(insight: AIProjectInsight, projectData?: any): Promise<AIActionResult> {
    const actions: AIAction[] = [];

    // Create a generic follow-up task
    actions.push({
      id: 'follow-up-insight',
      type: 'create_task',
      title: `Follow up on: ${insight.title}`,
      description: `Address AI insight: ${insight.description}`,
      data: {
        title: `Follow up on: ${insight.title}`,
        description: `AI Insight: ${insight.description}\n\nAction Items:\n${insight.actionItems.map(item => `• ${item}`).join('\n')}`,
        priority: insight.impact === 'high' ? 'high' : 'medium',
        estimatedTime: 60,
        tags: ['ai-insight', 'follow-up'],
      },
      priority: insight.impact === 'high' ? 'high' : 'medium',
      estimatedTime: 60,
    });

    return {
      success: true,
      message: `Follow-up actions generated for: ${insight.title}`,
      actions,
      data: {
        insightType: insight.type,
        confidence: insight.confidence,
        actionItems: insight.actionItems,
      },
    };
  }

  private calculateMilestoneDate(): string {
    // Calculate a reasonable milestone date (2-4 weeks from now)
    const date = new Date();
    date.setDate(date.getDate() + (14 + Math.floor(Math.random() * 14))); // 2-4 weeks
    return date.toISOString().split('T')[0];
  }

  async executeAction(action: AIAction, projectData?: any): Promise<AIActionResult> {
    try {
      console.log('Executing AI action:', action);

      switch (action.type) {
        case 'create_task':
          return this.executeCreateTask(action, projectData);
        case 'schedule_meeting':
          return this.executeScheduleMeeting(action, projectData);
        case 'send_notification':
          return this.executeSendNotification(action, projectData);
        case 'update_status':
          return this.executeUpdateStatus(action, projectData);
        case 'reassign_task':
          return this.executeReassignTask(action, projectData);
        case 'adjust_deadline':
          return this.executeAdjustDeadline(action, projectData);
        case 'create_milestone':
          return this.executeCreateMilestone(action, projectData);
        case 'generate_report':
          return this.executeGenerateReport(action, projectData);
        default:
          return {
            success: false,
            message: `Unknown action type: ${action.type}`,
          };
      }
    } catch (error) {
      console.error('Error executing action:', error);
      return {
        success: false,
        message: 'Failed to execute action. Please try again.',
      };
    }
  }

  private async executeCreateTask(action: AIAction, projectData?: any): Promise<AIActionResult> {
    // This would integrate with the actual task creation system
    console.log('Creating task from AI action:', action.data);
    
    return {
      success: true,
      message: `Task "${action.data.title}" has been created successfully.`,
      data: {
        taskId: `ai-task-${Date.now()}`,
        task: action.data,
      },
    };
  }

  private async executeScheduleMeeting(action: AIAction, projectData?: any): Promise<AIActionResult> {
    console.log('Scheduling meeting from AI action:', action.data);
    
    return {
      success: true,
      message: `Meeting "${action.title}" has been scheduled.`,
      data: {
        meetingId: `ai-meeting-${Date.now()}`,
        meeting: action.data,
      },
    };
  }

  private async executeSendNotification(action: AIAction, projectData?: any): Promise<AIActionResult> {
    console.log('Sending notification from AI action:', action.data);
    
    return {
      success: true,
      message: `Notification sent: ${action.title}`,
      data: action.data,
    };
  }

  private async executeUpdateStatus(action: AIAction, projectData?: any): Promise<AIActionResult> {
    console.log('Updating status from AI action:', action.data);
    
    return {
      success: true,
      message: `Status updated: ${action.title}`,
      data: action.data,
    };
  }

  private async executeReassignTask(action: AIAction, projectData?: any): Promise<AIActionResult> {
    console.log('Reassigning tasks from AI action:', action.data);
    
    return {
      success: true,
      message: `Tasks reassigned based on workload analysis.`,
      data: action.data,
    };
  }

  private async executeAdjustDeadline(action: AIAction, projectData?: any): Promise<AIActionResult> {
    console.log('Adjusting deadlines from AI action:', action.data);
    
    return {
      success: true,
      message: `Deadlines adjusted for better project timeline.`,
      data: action.data,
    };
  }

  private async executeCreateMilestone(action: AIAction, projectData?: any): Promise<AIActionResult> {
    console.log('Creating milestone from AI action:', action.data);
    
    return {
      success: true,
      message: `Milestone "${action.data.title}" has been created.`,
      data: {
        milestoneId: `ai-milestone-${Date.now()}`,
        milestone: action.data,
      },
    };
  }

  private async executeGenerateReport(action: AIAction, projectData?: any): Promise<AIActionResult> {
    console.log('Generating report from AI action:', action.data);
    
    return {
      success: true,
      message: `Report "${action.title}" has been generated.`,
      data: {
        reportId: `ai-report-${Date.now()}`,
        report: action.data,
      },
    };
  }
}

export const aiActionHandler = new AIActionHandler();
