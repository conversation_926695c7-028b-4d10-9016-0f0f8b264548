import Groq from 'groq-sdk';

const groq = new Groq({
  apiKey: '********************************************************',
  dangerouslyAllowBrowser: true, // Only for demo - in production, use server-side
});

export interface AITaskSuggestion {
  title: string;
  description: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  estimatedTime: number; // in minutes
  tags: string[];
  assigneeSuggestion?: string;
}

export interface AIProjectInsight {
  type: 'risk' | 'opportunity' | 'recommendation' | 'milestone';
  title: string;
  description: string;
  confidence: number; // 0-1
  actionItems: string[];
  impact: 'low' | 'medium' | 'high';
}

export interface AIStandupReport {
  summary: string;
  completedTasks: string[];
  upcomingTasks: string[];
  blockers: string[];
  recommendations: string[];
}

class GroqAIService {
  private async makeRequest(prompt: string, systemPrompt?: string) {
    try {
      const completion = await groq.chat.completions.create({
        messages: [
          {
            role: 'system',
            content: systemPrompt || 'You are an AI assistant specialized in project management and productivity. Provide helpful, actionable insights.',
          },
          {
            role: 'user',
            content: prompt,
          },
        ],
        model: 'llama3-8b-8192',
        temperature: 0.7,
        max_tokens: 1024,
      });

      return completion.choices[0]?.message?.content || '';
    } catch (error) {
      console.error('Groq API error:', error);
      throw new Error('Failed to get AI response');
    }
  }

  async generateTaskSuggestions(projectContext: string, userPreferences?: any): Promise<AITaskSuggestion[]> {
    const prompt = `
Based on the following project context, suggest 3-5 relevant tasks that should be created:

Project Context: ${projectContext}
User Preferences: ${JSON.stringify(userPreferences || {})}

Please respond with a JSON array of task suggestions. Each task should have:
- title: string
- description: string
- priority: "low" | "medium" | "high" | "urgent"
- estimatedTime: number (in minutes)
- tags: string[]

Example format:
[
  {
    "title": "Setup project repository",
    "description": "Initialize Git repository and setup basic project structure",
    "priority": "high",
    "estimatedTime": 120,
    "tags": ["setup", "development"]
  }
]
`;

    try {
      const response = await this.makeRequest(prompt, 'You are a project management AI that suggests relevant tasks. Always respond with valid JSON.');
      const suggestions = JSON.parse(response);
      return Array.isArray(suggestions) ? suggestions : [];
    } catch (error) {
      console.error('Error generating task suggestions:', error);
      return [];
    }
  }

  async analyzeProjectRisks(projectData: any): Promise<AIProjectInsight[]> {
    const prompt = `
Analyze the following project data and identify potential risks, opportunities, and recommendations:

Project Data: ${JSON.stringify(projectData)}

Please respond with a JSON array of insights. Each insight should have:
- type: "risk" | "opportunity" | "recommendation" | "milestone"
- title: string
- description: string
- confidence: number (0-1)
- actionItems: string[]
- impact: "low" | "medium" | "high"

Focus on:
1. Timeline risks (overdue tasks, unrealistic deadlines)
2. Resource allocation issues
3. Task dependencies and blockers
4. Team workload distribution
5. Opportunities for optimization
`;

    try {
      const response = await this.makeRequest(prompt, 'You are a project risk analysis AI. Always respond with valid JSON.');
      const insights = JSON.parse(response);
      return Array.isArray(insights) ? insights : [];
    } catch (error) {
      console.error('Error analyzing project risks:', error);
      return [];
    }
  }

  async generateStandupReport(teamData: any, timeframe: string = 'daily'): Promise<AIStandupReport> {
    const prompt = `
Generate a ${timeframe} standup report based on the following team data:

Team Data: ${JSON.stringify(teamData)}

Please respond with a JSON object containing:
- summary: string (brief overview of team progress)
- completedTasks: string[] (tasks completed in the timeframe)
- upcomingTasks: string[] (tasks planned for next period)
- blockers: string[] (identified blockers or issues)
- recommendations: string[] (actionable recommendations)

Focus on:
1. Progress against goals
2. Team velocity and productivity
3. Potential blockers or risks
4. Recommendations for improvement
`;

    try {
      const response = await this.makeRequest(prompt, 'You are an AI that generates team standup reports. Always respond with valid JSON.');
      const report = JSON.parse(response);
      return report;
    } catch (error) {
      console.error('Error generating standup report:', error);
      return {
        summary: 'Unable to generate report at this time.',
        completedTasks: [],
        upcomingTasks: [],
        blockers: [],
        recommendations: [],
      };
    }
  }

  async optimizeTaskAssignment(tasks: any[], teamMembers: any[]): Promise<{ taskId: string; suggestedAssignee: string; reason: string }[]> {
    const prompt = `
Optimize task assignments based on the following data:

Tasks: ${JSON.stringify(tasks)}
Team Members: ${JSON.stringify(teamMembers)}

Please respond with a JSON array of assignment suggestions. Each suggestion should have:
- taskId: string
- suggestedAssignee: string (team member ID or name)
- reason: string (explanation for the suggestion)

Consider:
1. Team member skills and expertise
2. Current workload distribution
3. Task complexity and requirements
4. Deadlines and priorities
5. Team member availability
`;

    try {
      const response = await this.makeRequest(prompt, 'You are an AI that optimizes task assignments. Always respond with valid JSON.');
      const suggestions = JSON.parse(response);
      return Array.isArray(suggestions) ? suggestions : [];
    } catch (error) {
      console.error('Error optimizing task assignment:', error);
      return [];
    }
  }

  async generateProjectDescription(projectName: string, requirements: string): Promise<string> {
    const prompt = `
Generate a comprehensive project description for a project named "${projectName}" with the following requirements:

Requirements: ${requirements}

Please provide:
1. A clear project overview
2. Key objectives and goals
3. Scope and deliverables
4. Success criteria
5. Potential challenges and mitigation strategies

Format the response as a well-structured project description that could be used in project documentation.
`;

    try {
      const response = await this.makeRequest(prompt, 'You are a project management AI that creates detailed project descriptions.');
      return response;
    } catch (error) {
      console.error('Error generating project description:', error);
      return 'Unable to generate project description at this time.';
    }
  }

  async estimateTaskTime(taskDescription: string, complexity: string = 'medium'): Promise<{ estimatedTime: number; confidence: number; factors: string[] }> {
    const prompt = `
Estimate the time required to complete the following task:

Task Description: ${taskDescription}
Complexity Level: ${complexity}

Please respond with a JSON object containing:
- estimatedTime: number (in minutes)
- confidence: number (0-1, how confident you are in the estimate)
- factors: string[] (factors that influenced the estimate)

Consider:
1. Task complexity and scope
2. Typical development/work patterns
3. Potential blockers or dependencies
4. Testing and review time
5. Documentation requirements
`;

    try {
      const response = await this.makeRequest(prompt, 'You are an AI that provides accurate task time estimates. Always respond with valid JSON.');
      const estimate = JSON.parse(response);
      return estimate;
    } catch (error) {
      console.error('Error estimating task time:', error);
      return {
        estimatedTime: 120, // Default 2 hours
        confidence: 0.5,
        factors: ['Unable to analyze - using default estimate'],
      };
    }
  }

  async generateMeetingAgenda(meetingType: string, participants: string[], context: string): Promise<string[]> {
    const prompt = `
Generate a meeting agenda for a ${meetingType} meeting with the following context:

Participants: ${participants.join(', ')}
Context: ${context}

Please respond with a JSON array of agenda items (strings).

Consider:
1. Meeting objectives
2. Participant roles and interests
3. Time efficiency
4. Actionable outcomes
5. Follow-up requirements

Example format: ["Welcome and introductions", "Project status review", "Risk assessment", "Action items and next steps"]
`;

    try {
      const response = await this.makeRequest(prompt, 'You are an AI that creates effective meeting agendas. Always respond with valid JSON array.');
      const agenda = JSON.parse(response);
      return Array.isArray(agenda) ? agenda : [];
    } catch (error) {
      console.error('Error generating meeting agenda:', error);
      return ['Meeting overview', 'Discussion topics', 'Action items', 'Next steps'];
    }
  }
}

export const groqAI = new GroqAIService();
