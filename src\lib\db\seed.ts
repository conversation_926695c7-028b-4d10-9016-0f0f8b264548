import { db, users, folders, projects, projectMembers, taskStatuses, tasks, comments, timeEntries, tags } from './index';

export async function seedDatabase() {
  try {
    console.log('🌱 Seeding database...');

    // Create users
    const seedUsers = await db.insert(users).values([
      {
        name: '<PERSON>',
        email: '<EMAIL>',
        avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=32&h=32&fit=crop&crop=face',
        role: 'admin',
      },
      {
        name: '<PERSON>',
        email: '<EMAIL>',
        avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=32&h=32&fit=crop&crop=face',
        role: 'member',
      },
      {
        name: '<PERSON>',
        email: '<EMAIL>',
        avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=32&h=32&fit=crop&crop=face',
        role: 'member',
      },
    ]).returning();

    console.log('✅ Users created');

    // Create folders
    const seedFolders = await db.insert(folders).values([
      {
        name: 'Client Projects',
        description: 'All client-related projects',
        color: '#3b82f6',
        userId: seedUsers[0].id,
      },
      {
        name: 'Internal Projects',
        description: 'Company internal initiatives',
        color: '#22c55e',
        userId: seedUsers[0].id,
      },
    ]).returning();

    console.log('✅ Folders created');

    // Create projects
    const seedProjects = await db.insert(projects).values([
      {
        name: 'Website Redesign',
        description: 'Complete redesign of company website',
        color: '#3b82f6',
        folderId: seedFolders[0].id,
        ownerId: seedUsers[0].id,
        settings: {
          isPublic: false,
          allowGuestAccess: true,
          defaultTaskStatus: null,
          customStatuses: [],
          timeTracking: true,
        },
      },
      {
        name: 'Mobile App Development',
        description: 'Native mobile app for iOS and Android',
        color: '#22c55e',
        folderId: seedFolders[0].id,
        ownerId: seedUsers[0].id,
        settings: {
          isPublic: false,
          allowGuestAccess: false,
          defaultTaskStatus: null,
          customStatuses: [],
          timeTracking: true,
        },
      },
    ]).returning();

    console.log('✅ Projects created');

    // Create project members
    await db.insert(projectMembers).values([
      // Website Redesign members
      { projectId: seedProjects[0].id, userId: seedUsers[0].id, role: 'owner' },
      { projectId: seedProjects[0].id, userId: seedUsers[1].id, role: 'member' },
      { projectId: seedProjects[0].id, userId: seedUsers[2].id, role: 'member' },
      // Mobile App members
      { projectId: seedProjects[1].id, userId: seedUsers[0].id, role: 'owner' },
      { projectId: seedProjects[1].id, userId: seedUsers[2].id, role: 'member' },
    ]);

    console.log('✅ Project members created');

    // Create default task statuses
    const seedStatuses = await db.insert(taskStatuses).values([
      { name: 'To Do', color: '#6b7280', order: 1, isDefault: true },
      { name: 'In Progress', color: '#3b82f6', order: 2 },
      { name: 'In Review', color: '#f59e0b', order: 3 },
      { name: 'Done', color: '#22c55e', order: 4, isCompleted: true },
    ]).returning();

    console.log('✅ Task statuses created');

    // Create tags
    const seedTags = await db.insert(tags).values([
      { name: 'Frontend', color: '#3b82f6', projectId: seedProjects[0].id, createdBy: seedUsers[0].id },
      { name: 'Backend', color: '#22c55e', projectId: seedProjects[0].id, createdBy: seedUsers[0].id },
      { name: 'Design', color: '#ec4899', projectId: seedProjects[0].id, createdBy: seedUsers[1].id },
      { name: 'Bug', color: '#ef4444', createdBy: seedUsers[0].id },
      { name: 'Feature', color: '#8b5cf6', createdBy: seedUsers[0].id },
    ]).returning();

    console.log('✅ Tags created');

    // Create tasks
    const seedTasks = await db.insert(tasks).values([
      {
        title: 'Design homepage mockup',
        description: 'Create wireframes and high-fidelity mockups for the new homepage design',
        projectId: seedProjects[0].id,
        assigneeId: seedUsers[1].id,
        creatorId: seedUsers[0].id,
        statusId: seedStatuses[1].id, // In Progress
        priority: 'high',
        tags: [seedTags[2].id], // Design tag
        startDate: new Date('2024-01-10'),
        dueDate: new Date('2024-01-20'),
        estimatedTime: 480, // 8 hours
        actualTime: 360, // 6 hours
        position: 1,
      },
      {
        title: 'Implement responsive navigation',
        description: 'Build responsive navigation component with mobile menu',
        projectId: seedProjects[0].id,
        assigneeId: seedUsers[2].id,
        creatorId: seedUsers[0].id,
        statusId: seedStatuses[0].id, // To Do
        priority: 'medium',
        tags: [seedTags[0].id], // Frontend tag
        dueDate: new Date('2024-01-25'),
        estimatedTime: 240, // 4 hours
        position: 2,
      },
      {
        title: 'Set up database schema',
        description: 'Design and implement database schema for user management',
        projectId: seedProjects[1].id,
        assigneeId: seedUsers[0].id,
        creatorId: seedUsers[0].id,
        statusId: seedStatuses[3].id, // Done
        priority: 'high',
        tags: [seedTags[1].id], // Backend tag
        startDate: new Date('2024-01-08'),
        dueDate: new Date('2024-01-15'),
        estimatedTime: 360, // 6 hours
        actualTime: 420, // 7 hours
        completedAt: new Date('2024-01-14'),
        position: 1,
      },
      {
        title: 'User authentication flow',
        description: 'Implement login, registration, and password reset functionality',
        projectId: seedProjects[1].id,
        assigneeId: seedUsers[2].id,
        creatorId: seedUsers[0].id,
        statusId: seedStatuses[2].id, // In Review
        priority: 'urgent',
        tags: [seedTags[1].id, seedTags[4].id], // Backend and Feature tags
        startDate: new Date('2024-01-15'),
        dueDate: new Date('2024-01-22'),
        estimatedTime: 600, // 10 hours
        actualTime: 480, // 8 hours
        position: 2,
      },
    ]).returning();

    console.log('✅ Tasks created');

    // Create comments
    await db.insert(comments).values([
      {
        content: 'Great progress on the mockups! The color scheme looks perfect.',
        taskId: seedTasks[0].id,
        userId: seedUsers[0].id,
        mentions: [],
      },
      {
        content: 'Thanks! I\'ll have the final version ready by tomorrow. Can you review the mobile version?',
        taskId: seedTasks[0].id,
        userId: seedUsers[1].id,
        mentions: [seedUsers[2].id],
      },
    ]);

    console.log('✅ Comments created');

    // Create time entries
    await db.insert(timeEntries).values([
      {
        taskId: seedTasks[0].id,
        userId: seedUsers[1].id,
        description: 'Working on homepage wireframes',
        startTime: new Date('2024-01-15T09:00:00'),
        endTime: new Date('2024-01-15T12:00:00'),
        duration: 180,
        isRunning: false,
        billable: true,
      },
      {
        taskId: seedTasks[0].id,
        userId: seedUsers[1].id,
        description: 'High-fidelity mockups',
        startTime: new Date('2024-01-16T14:00:00'),
        endTime: new Date('2024-01-16T17:00:00'),
        duration: 180,
        isRunning: false,
        billable: true,
      },
    ]);

    console.log('✅ Time entries created');
    console.log('🎉 Database seeded successfully!');

    return {
      users: seedUsers,
      folders: seedFolders,
      projects: seedProjects,
      statuses: seedStatuses,
      tasks: seedTasks,
      tags: seedTags,
    };
  } catch (error) {
    console.error('❌ Error seeding database:', error);
    throw error;
  }
}
