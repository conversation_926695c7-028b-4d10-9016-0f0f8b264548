'use client';

export interface GoogleCalendarEvent {
  id: string;
  summary: string;
  description?: string;
  start: {
    dateTime?: string;
    date?: string;
    timeZone?: string;
  };
  end: {
    dateTime?: string;
    date?: string;
    timeZone?: string;
  };
  attendees?: Array<{
    email: string;
    displayName?: string;
    responseStatus?: 'needsAction' | 'declined' | 'tentative' | 'accepted';
  }>;
  location?: string;
  htmlLink?: string;
}

export interface CalendarIntegrationSettings {
  enabled: boolean;
  calendarId: string;
  syncTasks: boolean;
  syncDeadlines: boolean;
  reminderMinutes: number;
}

class GoogleCalendarIntegration {
  private isInitialized = false;
  private accessToken: string | null = null;

  async initialize() {
    if (this.isInitialized) return;

    try {
      // In a real implementation, you would:
      // 1. Load Google Calendar API
      // 2. Initialize OAuth 2.0
      // 3. Handle authentication flow
      
      console.log('🗓️ Google Calendar integration initialized');
      this.isInitialized = true;
    } catch (error) {
      console.error('Failed to initialize Google Calendar:', error);
      throw error;
    }
  }

  async authenticate(): Promise<boolean> {
    try {
      // Simulate OAuth flow
      // In real implementation, this would open Google OAuth popup
      console.log('🔐 Starting Google Calendar authentication...');
      
      // Simulate successful authentication
      await new Promise(resolve => setTimeout(resolve, 1000));
      this.accessToken = 'mock_access_token';
      
      console.log('✅ Google Calendar authenticated successfully');
      return true;
    } catch (error) {
      console.error('Google Calendar authentication failed:', error);
      return false;
    }
  }

  async disconnect() {
    this.accessToken = null;
    console.log('🔌 Disconnected from Google Calendar');
  }

  isAuthenticated(): boolean {
    return !!this.accessToken;
  }

  async getCalendars() {
    if (!this.isAuthenticated()) {
      throw new Error('Not authenticated with Google Calendar');
    }

    // Mock calendar list
    return [
      {
        id: 'primary',
        summary: 'Primary Calendar',
        description: 'Your main calendar',
        primary: true,
      },
      {
        id: '<EMAIL>',
        summary: 'Work Calendar',
        description: 'Work-related events',
        primary: false,
      },
      {
        id: '<EMAIL>',
        summary: 'Team Calendar',
        description: 'Team meetings and events',
        primary: false,
      },
    ];
  }

  async getEvents(calendarId: string, timeMin?: Date, timeMax?: Date): Promise<GoogleCalendarEvent[]> {
    if (!this.isAuthenticated()) {
      throw new Error('Not authenticated with Google Calendar');
    }

    // Mock events
    const mockEvents: GoogleCalendarEvent[] = [
      {
        id: 'event-1',
        summary: 'Project Kickoff Meeting',
        description: 'Initial meeting to discuss project requirements',
        start: {
          dateTime: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
          timeZone: 'America/New_York',
        },
        end: {
          dateTime: new Date(Date.now() + 24 * 60 * 60 * 1000 + 60 * 60 * 1000).toISOString(),
          timeZone: 'America/New_York',
        },
        attendees: [
          { email: '<EMAIL>', displayName: 'John Doe', responseStatus: 'accepted' },
          { email: '<EMAIL>', displayName: 'Jane Smith', responseStatus: 'tentative' },
        ],
        location: 'Conference Room A',
        htmlLink: 'https://calendar.google.com/event?eid=mock',
      },
      {
        id: 'event-2',
        summary: 'Design Review',
        description: 'Review latest design mockups',
        start: {
          dateTime: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000).toISOString(),
          timeZone: 'America/New_York',
        },
        end: {
          dateTime: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000 + 30 * 60 * 1000).toISOString(),
          timeZone: 'America/New_York',
        },
        attendees: [
          { email: '<EMAIL>', displayName: 'John Doe', responseStatus: 'accepted' },
        ],
        htmlLink: 'https://calendar.google.com/event?eid=mock2',
      },
    ];

    return mockEvents;
  }

  async createEvent(calendarId: string, event: Partial<GoogleCalendarEvent>): Promise<GoogleCalendarEvent> {
    if (!this.isAuthenticated()) {
      throw new Error('Not authenticated with Google Calendar');
    }

    // Mock event creation
    const newEvent: GoogleCalendarEvent = {
      id: `event-${Date.now()}`,
      summary: event.summary || 'Untitled Event',
      description: event.description,
      start: event.start || {
        dateTime: new Date().toISOString(),
        timeZone: 'America/New_York',
      },
      end: event.end || {
        dateTime: new Date(Date.now() + 60 * 60 * 1000).toISOString(),
        timeZone: 'America/New_York',
      },
      attendees: event.attendees,
      location: event.location,
      htmlLink: `https://calendar.google.com/event?eid=mock-${Date.now()}`,
    };

    console.log('📅 Created Google Calendar event:', newEvent);
    return newEvent;
  }

  async updateEvent(calendarId: string, eventId: string, updates: Partial<GoogleCalendarEvent>): Promise<GoogleCalendarEvent> {
    if (!this.isAuthenticated()) {
      throw new Error('Not authenticated with Google Calendar');
    }

    // Mock event update
    console.log('📝 Updated Google Calendar event:', eventId, updates);
    
    // Return mock updated event
    return {
      id: eventId,
      summary: updates.summary || 'Updated Event',
      ...updates,
    } as GoogleCalendarEvent;
  }

  async deleteEvent(calendarId: string, eventId: string): Promise<void> {
    if (!this.isAuthenticated()) {
      throw new Error('Not authenticated with Google Calendar');
    }

    console.log('🗑️ Deleted Google Calendar event:', eventId);
  }

  // Sync task to Google Calendar
  async syncTaskToCalendar(task: any, calendarId: string = 'primary'): Promise<GoogleCalendarEvent | null> {
    if (!this.isAuthenticated()) {
      return null;
    }

    try {
      const event = await this.createEvent(calendarId, {
        summary: `Task: ${task.title}`,
        description: task.description || `Task from Bordio: ${task.title}`,
        start: {
          dateTime: task.dueDate ? new Date(task.dueDate).toISOString() : new Date().toISOString(),
          timeZone: 'America/New_York',
        },
        end: {
          dateTime: task.dueDate 
            ? new Date(new Date(task.dueDate).getTime() + (task.estimatedTime || 60) * 60 * 1000).toISOString()
            : new Date(Date.now() + 60 * 60 * 1000).toISOString(),
          timeZone: 'America/New_York',
        },
      });

      return event;
    } catch (error) {
      console.error('Failed to sync task to calendar:', error);
      return null;
    }
  }

  // Sync project deadline to Google Calendar
  async syncProjectDeadline(project: any, deadline: Date, calendarId: string = 'primary'): Promise<GoogleCalendarEvent | null> {
    if (!this.isAuthenticated()) {
      return null;
    }

    try {
      const event = await this.createEvent(calendarId, {
        summary: `Project Deadline: ${project.name}`,
        description: `Project deadline for: ${project.name}\n\n${project.description || ''}`,
        start: {
          date: deadline.toISOString().split('T')[0], // All-day event
        },
        end: {
          date: deadline.toISOString().split('T')[0],
        },
      });

      return event;
    } catch (error) {
      console.error('Failed to sync project deadline to calendar:', error);
      return null;
    }
  }
}

// Singleton instance
export const googleCalendar = new GoogleCalendarIntegration();

// React hook for Google Calendar integration
export function useGoogleCalendar() {
  const [isAuthenticated, setIsAuthenticated] = React.useState(false);
  const [isLoading, setIsLoading] = React.useState(false);
  const [events, setEvents] = React.useState<GoogleCalendarEvent[]>([]);

  React.useEffect(() => {
    googleCalendar.initialize();
    setIsAuthenticated(googleCalendar.isAuthenticated());
  }, []);

  const authenticate = async () => {
    setIsLoading(true);
    try {
      const success = await googleCalendar.authenticate();
      setIsAuthenticated(success);
      return success;
    } finally {
      setIsLoading(false);
    }
  };

  const disconnect = async () => {
    await googleCalendar.disconnect();
    setIsAuthenticated(false);
    setEvents([]);
  };

  const loadEvents = async (calendarId: string = 'primary') => {
    if (!isAuthenticated) return;
    
    setIsLoading(true);
    try {
      const calendarEvents = await googleCalendar.getEvents(calendarId);
      setEvents(calendarEvents);
    } catch (error) {
      console.error('Failed to load calendar events:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return {
    isAuthenticated,
    isLoading,
    events,
    authenticate,
    disconnect,
    loadEvents,
    syncTaskToCalendar: googleCalendar.syncTaskToCalendar.bind(googleCalendar),
    syncProjectDeadline: googleCalendar.syncProjectDeadline.bind(googleCalendar),
  };
}

// Import React for the hook
import React from 'react';
