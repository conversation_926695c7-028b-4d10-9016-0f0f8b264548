import { 
  User, 
  Project, 
  Task, 
  Folder, 
  TaskStatus, 
  Comment, 
  TimeEntry, 
  CalendarEvent,
  Tag 
} from '@/types';
import { generateId } from './utils';

// Mock Users
export const mockUsers: User[] = [
  {
    id: 'user-1',
    name: '<PERSON>',
    email: '<EMAIL>',
    avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=32&h=32&fit=crop&crop=face',
    role: 'admin',
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
  },
  {
    id: 'user-2',
    name: '<PERSON>',
    email: '<EMAIL>',
    avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=32&h=32&fit=crop&crop=face',
    role: 'member',
    createdAt: new Date('2024-01-02'),
    updatedAt: new Date('2024-01-02'),
  },
  {
    id: 'user-3',
    name: '<PERSON>',
    email: '<EMAIL>',
    avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=32&h=32&fit=crop&crop=face',
    role: 'member',
    createdAt: new Date('2024-01-03'),
    updatedAt: new Date('2024-01-03'),
  },
];

// Mock Folders
export const mockFolders: Folder[] = [
  {
    id: 'folder-1',
    name: 'Client Projects',
    description: 'All client-related projects',
    color: '#3b82f6',
    userId: 'user-1',
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
  },
  {
    id: 'folder-2',
    name: 'Internal Projects',
    description: 'Company internal initiatives',
    color: '#22c55e',
    userId: 'user-1',
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
  },
];

// Mock Task Statuses
export const mockTaskStatuses: TaskStatus[] = [
  { id: 'status-1', name: 'To Do', color: '#6b7280', order: 1, isDefault: true },
  { id: 'status-2', name: 'In Progress', color: '#3b82f6', order: 2 },
  { id: 'status-3', name: 'In Review', color: '#f59e0b', order: 3 },
  { id: 'status-4', name: 'Done', color: '#22c55e', order: 4, isCompleted: true },
];

// Mock Projects
export const mockProjects: Project[] = [
  {
    id: 'project-1',
    name: 'Website Redesign',
    description: 'Complete redesign of company website',
    color: '#3b82f6',
    folderId: 'folder-1',
    ownerId: 'user-1',
    members: [
      { userId: 'user-1', role: 'owner', joinedAt: new Date('2024-01-01') },
      { userId: 'user-2', role: 'member', joinedAt: new Date('2024-01-02') },
      { userId: 'user-3', role: 'member', joinedAt: new Date('2024-01-03') },
    ],
    settings: {
      isPublic: false,
      allowGuestAccess: true,
      defaultTaskStatus: 'status-1',
      customStatuses: mockTaskStatuses,
      timeTracking: true,
    },
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-15'),
  },
  {
    id: 'project-2',
    name: 'Mobile App Development',
    description: 'Native mobile app for iOS and Android',
    color: '#22c55e',
    folderId: 'folder-1',
    ownerId: 'user-1',
    members: [
      { userId: 'user-1', role: 'owner', joinedAt: new Date('2024-01-05') },
      { userId: 'user-3', role: 'member', joinedAt: new Date('2024-01-06') },
    ],
    settings: {
      isPublic: false,
      allowGuestAccess: false,
      defaultTaskStatus: 'status-1',
      customStatuses: mockTaskStatuses,
      timeTracking: true,
    },
    createdAt: new Date('2024-01-05'),
    updatedAt: new Date('2024-01-20'),
  },
];

// Mock Tags
export const mockTags: Tag[] = [
  { id: 'tag-1', name: 'Frontend', color: '#3b82f6', projectId: 'project-1', createdBy: 'user-1', createdAt: new Date() },
  { id: 'tag-2', name: 'Backend', color: '#22c55e', projectId: 'project-1', createdBy: 'user-1', createdAt: new Date() },
  { id: 'tag-3', name: 'Design', color: '#ec4899', projectId: 'project-1', createdBy: 'user-2', createdAt: new Date() },
  { id: 'tag-4', name: 'Bug', color: '#ef4444', createdBy: 'user-1', createdAt: new Date() },
  { id: 'tag-5', name: 'Feature', color: '#8b5cf6', createdBy: 'user-1', createdAt: new Date() },
];

// Mock Tasks
export const mockTasks: Task[] = [
  {
    id: 'task-1',
    title: 'Design homepage mockup',
    description: 'Create wireframes and high-fidelity mockups for the new homepage design',
    projectId: 'project-1',
    assigneeId: 'user-2',
    creatorId: 'user-1',
    status: 'status-2',
    priority: 'high',
    tags: ['tag-3'],
    startDate: new Date('2024-01-10'),
    dueDate: new Date('2024-01-20'),
    estimatedTime: 480, // 8 hours
    actualTime: 360, // 6 hours
    attachments: [],
    comments: [],
    timeEntries: [],
    position: 1,
    createdAt: new Date('2024-01-10'),
    updatedAt: new Date('2024-01-15'),
  },
  {
    id: 'task-2',
    title: 'Implement responsive navigation',
    description: 'Build responsive navigation component with mobile menu',
    projectId: 'project-1',
    assigneeId: 'user-3',
    creatorId: 'user-1',
    status: 'status-1',
    priority: 'medium',
    tags: ['tag-1'],
    dueDate: new Date('2024-01-25'),
    estimatedTime: 240, // 4 hours
    attachments: [],
    comments: [],
    timeEntries: [],
    position: 2,
    createdAt: new Date('2024-01-12'),
    updatedAt: new Date('2024-01-12'),
  },
  {
    id: 'task-3',
    title: 'Set up database schema',
    description: 'Design and implement database schema for user management',
    projectId: 'project-2',
    assigneeId: 'user-1',
    creatorId: 'user-1',
    status: 'status-4',
    priority: 'high',
    tags: ['tag-2'],
    startDate: new Date('2024-01-08'),
    dueDate: new Date('2024-01-15'),
    estimatedTime: 360, // 6 hours
    actualTime: 420, // 7 hours
    completedAt: new Date('2024-01-14'),
    attachments: [],
    comments: [],
    timeEntries: [],
    position: 1,
    createdAt: new Date('2024-01-08'),
    updatedAt: new Date('2024-01-14'),
  },
  {
    id: 'task-4',
    title: 'User authentication flow',
    description: 'Implement login, registration, and password reset functionality',
    projectId: 'project-2',
    assigneeId: 'user-3',
    creatorId: 'user-1',
    status: 'status-3',
    priority: 'urgent',
    tags: ['tag-2', 'tag-5'],
    startDate: new Date('2024-01-15'),
    dueDate: new Date('2024-01-22'),
    estimatedTime: 600, // 10 hours
    actualTime: 480, // 8 hours
    attachments: [],
    comments: [],
    timeEntries: [],
    position: 2,
    createdAt: new Date('2024-01-15'),
    updatedAt: new Date('2024-01-18'),
  },
];

// Mock Comments
export const mockComments: Comment[] = [
  {
    id: 'comment-1',
    content: 'Great progress on the mockups! The color scheme looks perfect.',
    taskId: 'task-1',
    userId: 'user-1',
    mentions: [],
    attachments: [],
    createdAt: new Date('2024-01-16'),
    updatedAt: new Date('2024-01-16'),
  },
  {
    id: 'comment-2',
    content: 'Thanks! I\'ll have the final version ready by tomorrow. @user-3 can you review the mobile version?',
    taskId: 'task-1',
    userId: 'user-2',
    mentions: ['user-3'],
    attachments: [],
    createdAt: new Date('2024-01-16'),
    updatedAt: new Date('2024-01-16'),
  },
];

// Mock Time Entries
export const mockTimeEntries: TimeEntry[] = [
  {
    id: 'time-1',
    taskId: 'task-1',
    userId: 'user-2',
    description: 'Working on homepage wireframes',
    startTime: new Date('2024-01-15T09:00:00'),
    endTime: new Date('2024-01-15T12:00:00'),
    duration: 180,
    isRunning: false,
    billable: true,
    createdAt: new Date('2024-01-15'),
    updatedAt: new Date('2024-01-15'),
  },
  {
    id: 'time-2',
    taskId: 'task-1',
    userId: 'user-2',
    description: 'High-fidelity mockups',
    startTime: new Date('2024-01-16T14:00:00'),
    endTime: new Date('2024-01-16T17:00:00'),
    duration: 180,
    isRunning: false,
    billable: true,
    createdAt: new Date('2024-01-16'),
    updatedAt: new Date('2024-01-16'),
  },
];

// Mock Calendar Events
export const mockCalendarEvents: CalendarEvent[] = [
  {
    id: 'event-1',
    title: 'Project Kickoff Meeting',
    description: 'Initial meeting to discuss project requirements and timeline',
    startTime: new Date('2024-01-22T10:00:00'),
    endTime: new Date('2024-01-22T11:00:00'),
    allDay: false,
    projectId: 'project-1',
    userId: 'user-1',
    attendees: ['user-1', 'user-2', 'user-3'],
    location: 'Conference Room A',
    createdAt: new Date('2024-01-20'),
    updatedAt: new Date('2024-01-20'),
  },
  {
    id: 'event-2',
    title: 'Design Review',
    description: 'Review homepage mockups and provide feedback',
    startTime: new Date('2024-01-24T15:00:00'),
    endTime: new Date('2024-01-24T16:00:00'),
    allDay: false,
    taskId: 'task-1',
    projectId: 'project-1',
    userId: 'user-2',
    attendees: ['user-1', 'user-2'],
    createdAt: new Date('2024-01-22'),
    updatedAt: new Date('2024-01-22'),
  },
];

// Helper functions to get mock data
export function getCurrentUser(): User {
  return mockUsers[0]; // John Doe as current user
}

export function getUserById(id: string): User | undefined {
  return mockUsers.find(user => user.id === id);
}

export function getProjectById(id: string): Project | undefined {
  return mockProjects.find(project => project.id === id);
}

export function getTaskById(id: string): Task | undefined {
  return mockTasks.find(task => task.id === id);
}

export function getTasksByProject(projectId: string): Task[] {
  return mockTasks.filter(task => task.projectId === projectId);
}

export function getTasksByStatus(status: string): Task[] {
  return mockTasks.filter(task => task.status === status);
}

export function getTasksByAssignee(assigneeId: string): Task[] {
  return mockTasks.filter(task => task.assigneeId === assigneeId);
}
