'use client';

import { io, Socket } from 'socket.io-client';

export interface CollaborationEvent {
  type: 'task_updated' | 'task_created' | 'task_deleted' | 'comment_added' | 'user_typing' | 'user_presence';
  data: any;
  userId: string;
  timestamp: Date;
}

export interface UserPresence {
  userId: string;
  userName: string;
  avatar?: string;
  location: string; // e.g., 'project:123', 'task:456'
  lastSeen: Date;
}

class CollaborationManager {
  private socket: Socket | null = null;
  private listeners: Map<string, Function[]> = new Map();
  private currentUser: any = null;
  private presenceUsers: Map<string, UserPresence> = new Map();

  connect(userId: string, userName: string, avatar?: string) {
    if (this.socket?.connected) {
      return;
    }

    // In a real implementation, you'd connect to your WebSocket server
    // For demo purposes, we'll simulate real-time events
    this.currentUser = { userId, userName, avatar };
    
    // Simulate connection
    console.log('🔗 Connected to collaboration server');
    this.emit('connected', { userId, userName, avatar });
    
    // Simulate some real-time events for demo
    this.simulateRealTimeEvents();
  }

  disconnect() {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
    this.presenceUsers.clear();
    console.log('🔌 Disconnected from collaboration server');
  }

  // Subscribe to events
  on(eventType: string, callback: Function) {
    if (!this.listeners.has(eventType)) {
      this.listeners.set(eventType, []);
    }
    this.listeners.get(eventType)!.push(callback);
  }

  // Unsubscribe from events
  off(eventType: string, callback: Function) {
    const callbacks = this.listeners.get(eventType);
    if (callbacks) {
      const index = callbacks.indexOf(callback);
      if (index > -1) {
        callbacks.splice(index, 1);
      }
    }
  }

  // Emit events
  private emit(eventType: string, data: any) {
    const callbacks = this.listeners.get(eventType);
    if (callbacks) {
      callbacks.forEach(callback => callback(data));
    }
  }

  // Join a specific room (project, task, etc.)
  joinRoom(roomId: string) {
    console.log(`🏠 Joined room: ${roomId}`);
    this.updatePresence(roomId);
  }

  // Leave a room
  leaveRoom(roomId: string) {
    console.log(`🚪 Left room: ${roomId}`);
  }

  // Update user presence
  updatePresence(location: string) {
    if (!this.currentUser) return;

    const presence: UserPresence = {
      userId: this.currentUser.userId,
      userName: this.currentUser.userName,
      avatar: this.currentUser.avatar,
      location,
      lastSeen: new Date(),
    };

    this.presenceUsers.set(this.currentUser.userId, presence);
    this.emit('presence_updated', presence);
  }

  // Get users in current location
  getPresenceUsers(location?: string): UserPresence[] {
    const users = Array.from(this.presenceUsers.values());
    if (location) {
      return users.filter(user => user.location === location);
    }
    return users;
  }

  // Send typing indicator
  sendTyping(location: string, isTyping: boolean) {
    this.emit('user_typing', {
      userId: this.currentUser?.userId,
      userName: this.currentUser?.userName,
      location,
      isTyping,
      timestamp: new Date(),
    });
  }

  // Broadcast task update
  broadcastTaskUpdate(taskId: string, updates: any) {
    const event: CollaborationEvent = {
      type: 'task_updated',
      data: { taskId, updates },
      userId: this.currentUser?.userId || '',
      timestamp: new Date(),
    };

    this.emit('task_updated', event);
    console.log('📤 Broadcasted task update:', event);
  }

  // Broadcast comment added
  broadcastCommentAdded(taskId: string, comment: any) {
    const event: CollaborationEvent = {
      type: 'comment_added',
      data: { taskId, comment },
      userId: this.currentUser?.userId || '',
      timestamp: new Date(),
    };

    this.emit('comment_added', event);
    console.log('💬 Broadcasted comment added:', event);
  }

  // Simulate real-time events for demo
  private simulateRealTimeEvents() {
    // Simulate other users joining
    setTimeout(() => {
      this.presenceUsers.set('user-demo-1', {
        userId: 'user-demo-1',
        userName: 'Demo User 1',
        avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=32&h=32&fit=crop&crop=face',
        location: 'project:current',
        lastSeen: new Date(),
      });
      
      this.emit('presence_updated', this.presenceUsers.get('user-demo-1'));
    }, 2000);

    // Simulate typing indicators
    setTimeout(() => {
      this.emit('user_typing', {
        userId: 'user-demo-1',
        userName: 'Demo User 1',
        location: 'task:current',
        isTyping: true,
        timestamp: new Date(),
      });

      setTimeout(() => {
        this.emit('user_typing', {
          userId: 'user-demo-1',
          userName: 'Demo User 1',
          location: 'task:current',
          isTyping: false,
          timestamp: new Date(),
        });
      }, 3000);
    }, 5000);

    // Simulate task updates
    setTimeout(() => {
      this.emit('task_updated', {
        type: 'task_updated',
        data: {
          taskId: 'demo-task',
          updates: { title: 'Updated by Demo User 1' },
        },
        userId: 'user-demo-1',
        timestamp: new Date(),
      });
    }, 8000);
  }
}

// Singleton instance
export const collaborationManager = new CollaborationManager();

// React hook for collaboration
export function useCollaboration(userId?: string, userName?: string, avatar?: string) {
  const [presenceUsers, setPresenceUsers] = React.useState<UserPresence[]>([]);
  const [typingUsers, setTypingUsers] = React.useState<Map<string, any>>(new Map());

  React.useEffect(() => {
    if (userId && userName) {
      collaborationManager.connect(userId, userName, avatar);
    }

    const handlePresenceUpdate = (presence: UserPresence) => {
      setPresenceUsers(prev => {
        const filtered = prev.filter(u => u.userId !== presence.userId);
        return [...filtered, presence];
      });
    };

    const handleTyping = (data: any) => {
      setTypingUsers(prev => {
        const newMap = new Map(prev);
        if (data.isTyping) {
          newMap.set(data.userId, data);
        } else {
          newMap.delete(data.userId);
        }
        return newMap;
      });
    };

    collaborationManager.on('presence_updated', handlePresenceUpdate);
    collaborationManager.on('user_typing', handleTyping);

    return () => {
      collaborationManager.off('presence_updated', handlePresenceUpdate);
      collaborationManager.off('user_typing', handleTyping);
    };
  }, [userId, userName, avatar]);

  return {
    presenceUsers,
    typingUsers: Array.from(typingUsers.values()),
    joinRoom: collaborationManager.joinRoom.bind(collaborationManager),
    leaveRoom: collaborationManager.leaveRoom.bind(collaborationManager),
    sendTyping: collaborationManager.sendTyping.bind(collaborationManager),
    broadcastTaskUpdate: collaborationManager.broadcastTaskUpdate.bind(collaborationManager),
    broadcastCommentAdded: collaborationManager.broadcastCommentAdded.bind(collaborationManager),
  };
}

// Import React for the hook
import React from 'react';
