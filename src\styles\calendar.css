/* Custom styles for react-big-calendar */

.rbc-calendar {
  font-family: inherit;
}

.rbc-header {
  background-color: hsl(var(--muted));
  color: hsl(var(--muted-foreground));
  font-weight: 600;
  padding: 8px 12px;
  border-bottom: 1px solid hsl(var(--border));
}

.rbc-month-view {
  border: 1px solid hsl(var(--border));
  border-radius: 8px;
  overflow: hidden;
}

.rbc-date-cell {
  padding: 8px;
  border-right: 1px solid hsl(var(--border));
}

.rbc-date-cell:last-child {
  border-right: none;
}

.rbc-day-bg {
  border-bottom: 1px solid hsl(var(--border));
}

.rbc-day-bg:hover {
  background-color: hsl(var(--accent));
}

.rbc-off-range-bg {
  background-color: hsl(var(--muted) / 0.3);
}

.rbc-today {
  background-color: hsl(var(--primary) / 0.1);
}

.rbc-event {
  border-radius: 4px;
  padding: 2px 4px;
  font-size: 12px;
  line-height: 1.2;
  cursor: pointer;
  transition: all 0.2s ease;
}

.rbc-event:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.rbc-event-label {
  font-size: 10px;
  font-weight: 500;
}

.rbc-show-more {
  background-color: hsl(var(--secondary));
  color: hsl(var(--secondary-foreground));
  border: 1px solid hsl(var(--border));
  border-radius: 4px;
  padding: 2px 6px;
  font-size: 11px;
  cursor: pointer;
}

.rbc-show-more:hover {
  background-color: hsl(var(--accent));
}

/* Week and Day view styles */
.rbc-time-view {
  border: 1px solid hsl(var(--border));
  border-radius: 8px;
  overflow: hidden;
}

.rbc-time-header {
  border-bottom: 1px solid hsl(var(--border));
}

.rbc-time-content {
  border-top: none;
}

.rbc-timeslot-group {
  border-bottom: 1px solid hsl(var(--border));
}

.rbc-time-slot {
  border-top: 1px solid hsl(var(--border) / 0.3);
}

.rbc-current-time-indicator {
  background-color: hsl(var(--destructive));
  height: 2px;
  z-index: 3;
}

/* Agenda view styles */
.rbc-agenda-view {
  border: 1px solid hsl(var(--border));
  border-radius: 8px;
  overflow: hidden;
}

.rbc-agenda-view table {
  width: 100%;
}

.rbc-agenda-view .rbc-agenda-date-cell {
  background-color: hsl(var(--muted));
  border-right: 1px solid hsl(var(--border));
  padding: 12px;
  font-weight: 600;
  width: 120px;
}

.rbc-agenda-view .rbc-agenda-time-cell {
  background-color: hsl(var(--muted));
  border-right: 1px solid hsl(var(--border));
  padding: 12px;
  width: 100px;
  font-size: 12px;
  color: hsl(var(--muted-foreground));
}

.rbc-agenda-view .rbc-agenda-event-cell {
  padding: 12px;
  border-bottom: 1px solid hsl(var(--border));
}

.rbc-agenda-view .rbc-agenda-event-cell:hover {
  background-color: hsl(var(--accent));
}

/* Toolbar styles */
.rbc-toolbar {
  display: none; /* We're using custom toolbar */
}

/* Popup styles */
.rbc-overlay {
  background-color: hsl(var(--popover));
  border: 1px solid hsl(var(--border));
  border-radius: 8px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  padding: 8px;
  z-index: 1000;
}

.rbc-overlay-header {
  background-color: hsl(var(--muted));
  color: hsl(var(--muted-foreground));
  padding: 8px 12px;
  font-weight: 600;
  border-bottom: 1px solid hsl(var(--border));
  margin: -8px -8px 8px -8px;
  border-radius: 8px 8px 0 0;
}

/* Custom event priority colors */
.rbc-event.priority-urgent {
  background-color: #ef4444 !important;
  border-color: #dc2626 !important;
}

.rbc-event.priority-high {
  background-color: #f59e0b !important;
  border-color: #d97706 !important;
}

.rbc-event.priority-medium {
  background-color: #3b82f6 !important;
  border-color: #2563eb !important;
}

.rbc-event.priority-low {
  background-color: #22c55e !important;
  border-color: #16a34a !important;
}

.rbc-event.completed {
  background-color: #6b7280 !important;
  border-color: #4b5563 !important;
  opacity: 0.7;
}

.rbc-event.overdue {
  background-color: #ef4444 !important;
  border-color: #dc2626 !important;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .rbc-calendar {
    font-size: 12px;
  }
  
  .rbc-event {
    font-size: 10px;
    padding: 1px 2px;
  }
  
  .rbc-header {
    padding: 6px 8px;
    font-size: 12px;
  }
  
  .rbc-date-cell {
    padding: 4px;
  }
}
