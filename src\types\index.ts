// Core data types for Bordio work management platform

export interface User {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  role: 'admin' | 'member' | 'guest';
  createdAt: Date;
  updatedAt: Date;
}

export interface Folder {
  id: string;
  name: string;
  description?: string;
  color?: string;
  parentId?: string;
  userId: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface Project {
  id: string;
  name: string;
  description?: string;
  color?: string;
  folderId?: string;
  ownerId: string;
  members: ProjectMember[];
  settings: ProjectSettings;
  createdAt: Date;
  updatedAt: Date;
}

export interface ProjectMember {
  userId: string;
  role: 'owner' | 'admin' | 'member' | 'viewer';
  joinedAt: Date;
}

export interface ProjectSettings {
  isPublic: boolean;
  allowGuestAccess: boolean;
  defaultTaskStatus: string;
  customStatuses: TaskStatus[];
  timeTracking: boolean;
}

export interface TaskStatus {
  id: string;
  name: string;
  color: string;
  order: number;
  isDefault?: boolean;
  isCompleted?: boolean;
}

export interface Task {
  id: string;
  title: string;
  description?: string;
  projectId: string;
  parentTaskId?: string; // For subtasks
  assigneeId?: string;
  creatorId: string;
  status: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  tags: string[];
  startDate?: Date;
  dueDate?: Date;
  estimatedTime?: number; // in minutes
  actualTime?: number; // in minutes
  completedAt?: Date;
  attachments: Attachment[];
  comments: Comment[];
  timeEntries: TimeEntry[];
  position: number; // For ordering
  createdAt: Date;
  updatedAt: Date;
}

export interface Subtask {
  id: string;
  title: string;
  completed: boolean;
  parentTaskId: string;
  assigneeId?: string;
  estimatedTime?: number;
  actualTime?: number;
  order: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface Comment {
  id: string;
  content: string;
  taskId: string;
  userId: string;
  parentCommentId?: string; // For threaded comments
  mentions: string[]; // User IDs mentioned
  attachments: Attachment[];
  createdAt: Date;
  updatedAt: Date;
}

export interface Attachment {
  id: string;
  filename: string;
  originalName: string;
  mimeType: string;
  size: number;
  url: string;
  taskId?: string;
  commentId?: string;
  uploadedBy: string;
  createdAt: Date;
}

export interface TimeEntry {
  id: string;
  taskId: string;
  userId: string;
  description?: string;
  startTime: Date;
  endTime?: Date;
  duration: number; // in minutes
  isRunning: boolean;
  billable: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface CalendarEvent {
  id: string;
  title: string;
  description?: string;
  startTime: Date;
  endTime: Date;
  allDay: boolean;
  taskId?: string;
  projectId?: string;
  userId: string;
  recurrence?: RecurrenceRule;
  attendees: string[]; // User IDs
  location?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface RecurrenceRule {
  frequency: 'daily' | 'weekly' | 'monthly' | 'yearly';
  interval: number;
  daysOfWeek?: number[]; // 0-6, Sunday = 0
  dayOfMonth?: number;
  weekOfMonth?: number;
  endDate?: Date;
  occurrences?: number;
}

export interface Tag {
  id: string;
  name: string;
  color: string;
  projectId?: string; // If null, it's a global tag
  createdBy: string;
  createdAt: Date;
}

// UI State types
export interface ViewSettings {
  currentView: 'list' | 'board' | 'calendar' | 'timeline';
  filters: FilterSettings;
  groupBy?: 'status' | 'assignee' | 'priority' | 'dueDate';
  sortBy?: 'dueDate' | 'priority' | 'createdAt' | 'title';
  sortOrder: 'asc' | 'desc';
}

export interface FilterSettings {
  assignees: string[];
  statuses: string[];
  priorities: string[];
  tags: string[];
  dateRange?: {
    start: Date;
    end: Date;
  };
  showCompleted: boolean;
}

// API Response types
export interface ApiResponse<T> {
  data: T;
  success: boolean;
  message?: string;
  errors?: string[];
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Form types
export interface CreateProjectForm {
  name: string;
  description?: string;
  color?: string;
  folderId?: string;
  isPublic: boolean;
  allowGuestAccess: boolean;
}

export interface CreateTaskForm {
  title: string;
  description?: string;
  projectId: string;
  assigneeId?: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  tags: string[];
  startDate?: Date;
  dueDate?: Date;
  estimatedTime?: number;
}

export interface UpdateTaskForm extends Partial<CreateTaskForm> {
  status?: string;
  position?: number;
}
